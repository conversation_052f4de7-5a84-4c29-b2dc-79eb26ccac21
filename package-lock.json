<<<<<<<
{



  "name": "privacy-ai-assistant",



  "version": "0.1.0",



  "lockfileVersion": 3,



  "requires": true,



  "packages": {



    "": {



      "name": "privacy-ai-assistant",



      "version": "0.1.0",



      "license": "MIT",



      "dependencies": {



        "@tauri-apps/api": "^2.0.0",



        "@tauri-apps/plugin-fs": "^2.4.0",



        "@tauri-apps/plugin-http": "^2.3.0",



        "@tauri-apps/plugin-shell": "^2.3.0",



        "clsx": "^2.0.0",



        "lucide-react": "^0.300.0",



        "react": "^18.2.0",



        "react-dom": "^18.2.0",



        "react-markdown": "^9.0.1",



        "tailwind-merge": "^2.2.0",



        "zustand": "^4.4.7"



      },



      "devDependencies": {



        "@tauri-apps/cli": "^2.0.0",



        "@types/react": "^18.2.43",



        "@types/react-dom": "^18.2.17",



        "@typescript-eslint/eslint-plugin": "^6.14.0",



        "@typescript-eslint/parser": "^6.14.0",



        "@vitejs/plugin-react": "^4.2.1",



        "autoprefixer": "^10.4.16",



        "eslint": "^8.55.0",



        "eslint-plugin-react-hooks": "^4.6.0",



        "eslint-plugin-react-refresh": "^0.4.5",



        "postcss": "^8.4.32",



        "prettier": "^3.1.1",



        "tailwindcss": "^3.3.6",



        "typescript": "^5.2.2",



        "vite": "^5.0.8"



      },



      "engines": {



        "node": ">=18.0.0"



      }



    },



    "node_modules/@alloc/quick-lru": {



      "version": "5.2.0",



      "resolved": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz",



      "integrity": "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/@ampproject/remapping": {



      "version": "2.3.0",



      "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz",



      "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==",



      "dev": true,



      "license": "Apache-2.0",



      "dependencies": {



        "@jridgewell/gen-mapping": "^0.3.5",



        "@jridgewell/trace-mapping": "^0.3.24"



      },



      "engines": {



        "node": ">=6.0.0"



      }



    },



    "node_modules/@babel/code-frame": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz",



      "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/helper-validator-identifier": "^7.27.1",



        "js-tokens": "^4.0.0",



        "picocolors": "^1.1.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/compat-data": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz",



      "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/core": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz",



      "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@ampproject/remapping": "^2.2.0",



        "@babel/code-frame": "^7.27.1",



        "@babel/generator": "^7.28.0",



        "@babel/helper-compilation-targets": "^7.27.2",



        "@babel/helper-module-transforms": "^7.27.3",



        "@babel/helpers": "^7.27.6",



        "@babel/parser": "^7.28.0",



        "@babel/template": "^7.27.2",



        "@babel/traverse": "^7.28.0",



        "@babel/types": "^7.28.0",



        "convert-source-map": "^2.0.0",



        "debug": "^4.1.0",



        "gensync": "^1.0.0-beta.2",



        "json5": "^2.2.3",



        "semver": "^6.3.1"



      },



      "engines": {



        "node": ">=6.9.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/babel"



      }



    },



    "node_modules/@babel/core/node_modules/semver": {



      "version": "6.3.1",



      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",



      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",



      "dev": true,



      "license": "ISC",



      "bin": {



        "semver": "bin/semver.js"



      }



    },



    "node_modules/@babel/generator": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz",



      "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/parser": "^7.28.0",



        "@babel/types": "^7.28.0",



        "@jridgewell/gen-mapping": "^0.3.12",



        "@jridgewell/trace-mapping": "^0.3.28",



        "jsesc": "^3.0.2"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-compilation-targets": {



      "version": "7.27.2",



      "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz",



      "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/compat-data": "^7.27.2",



        "@babel/helper-validator-option": "^7.27.1",



        "browserslist": "^4.24.0",



        "lru-cache": "^5.1.1",



        "semver": "^6.3.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-compilation-targets/node_modules/semver": {



      "version": "6.3.1",



      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",



      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",



      "dev": true,



      "license": "ISC",



      "bin": {



        "semver": "bin/semver.js"



      }



    },



    "node_modules/@babel/helper-globals": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz",



      "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-module-imports": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz",



      "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/traverse": "^7.27.1",



        "@babel/types": "^7.27.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-module-transforms": {



      "version": "7.27.3",



      "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz",



      "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/helper-module-imports": "^7.27.1",



        "@babel/helper-validator-identifier": "^7.27.1",



        "@babel/traverse": "^7.27.3"



      },



      "engines": {



        "node": ">=6.9.0"



      },



      "peerDependencies": {



        "@babel/core": "^7.0.0"



      }



    },



    "node_modules/@babel/helper-plugin-utils": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz",



      "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-string-parser": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz",



      "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-validator-identifier": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz",



      "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helper-validator-option": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz",



      "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/helpers": {



      "version": "7.27.6",



      "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz",



      "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/template": "^7.27.2",



        "@babel/types": "^7.27.6"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/parser": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz",



      "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/types": "^7.28.0"



      },



      "bin": {



        "parser": "bin/babel-parser.js"



      },



      "engines": {



        "node": ">=6.0.0"



      }



    },



    "node_modules/@babel/plugin-transform-react-jsx-self": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz",



      "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/helper-plugin-utils": "^7.27.1"



      },



      "engines": {



        "node": ">=6.9.0"



      },



      "peerDependencies": {



        "@babel/core": "^7.0.0-0"



      }



    },



    "node_modules/@babel/plugin-transform-react-jsx-source": {



      "version": "7.27.1",



      "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz",



      "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/helper-plugin-utils": "^7.27.1"



      },



      "engines": {



        "node": ">=6.9.0"



      },



      "peerDependencies": {



        "@babel/core": "^7.0.0-0"



      }



    },



    "node_modules/@babel/template": {



      "version": "7.27.2",



      "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz",



      "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/code-frame": "^7.27.1",



        "@babel/parser": "^7.27.2",



        "@babel/types": "^7.27.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/traverse": {



      "version": "7.28.0",



      "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz",



      "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/code-frame": "^7.27.1",



        "@babel/generator": "^7.28.0",



        "@babel/helper-globals": "^7.28.0",



        "@babel/parser": "^7.28.0",



        "@babel/template": "^7.27.2",



        "@babel/types": "^7.28.0",



        "debug": "^4.3.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@babel/types": {



      "version": "7.28.1",



      "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz",



      "integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/helper-string-parser": "^7.27.1",



        "@babel/helper-validator-identifier": "^7.27.1"



      },



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/@esbuild/aix-ppc64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz",



      "integrity": "sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==",



      "cpu": [



        "ppc64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "aix"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/android-arm": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.5.tgz",



      "integrity": "sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "android"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/android-arm64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz",



      "integrity": "sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "android"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/android-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.21.5.tgz",



      "integrity": "sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "android"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/darwin-arm64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz",



      "integrity": "sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "darwin"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/darwin-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz",



      "integrity": "sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "darwin"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/freebsd-arm64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz",



      "integrity": "sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "freebsd"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/freebsd-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz",



      "integrity": "sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "freebsd"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-arm": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz",



      "integrity": "sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-arm64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz",



      "integrity": "sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-ia32": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz",



      "integrity": "sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==",



      "cpu": [



        "ia32"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-loong64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz",



      "integrity": "sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==",



      "cpu": [



        "loong64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-mips64el": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz",



      "integrity": "sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==",



      "cpu": [



        "mips64el"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-ppc64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz",



      "integrity": "sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==",



      "cpu": [



        "ppc64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-riscv64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz",



      "integrity": "sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==",



      "cpu": [



        "riscv64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-s390x": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz",



      "integrity": "sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==",



      "cpu": [



        "s390x"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/linux-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz",



      "integrity": "sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/netbsd-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz",



      "integrity": "sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "netbsd"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/openbsd-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz",



      "integrity": "sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "openbsd"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/sunos-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz",



      "integrity": "sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "sunos"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/win32-arm64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz",



      "integrity": "sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/win32-ia32": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz",



      "integrity": "sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==",



      "cpu": [



        "ia32"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@esbuild/win32-x64": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz",



      "integrity": "sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@eslint-community/eslint-utils": {



      "version": "4.7.0",



      "resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz",



      "integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "eslint-visitor-keys": "^3.4.3"



      },



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      },



      "peerDependencies": {



        "eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"



      }



    },



    "node_modules/@eslint-community/regexpp": {



      "version": "4.12.1",



      "resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz",



      "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": "^12.0.0 || ^14.0.0 || >=16.0.0"



      }



    },



    "node_modules/@eslint/eslintrc": {



      "version": "2.1.4",



      "resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz",



      "integrity": "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ajv": "^6.12.4",



        "debug": "^4.3.2",



        "espree": "^9.6.0",



        "globals": "^13.19.0",



        "ignore": "^5.2.0",



        "import-fresh": "^3.2.1",



        "js-yaml": "^4.1.0",



        "minimatch": "^3.1.2",



        "strip-json-comments": "^3.1.1"



      },



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      }



    },



    "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {



      "version": "1.1.12",



      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",



      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "balanced-match": "^1.0.0",



        "concat-map": "0.0.1"



      }



    },



    "node_modules/@eslint/eslintrc/node_modules/minimatch": {



      "version": "3.1.2",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",



      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^1.1.7"



      },



      "engines": {



        "node": "*"



      }



    },



    "node_modules/@eslint/js": {



      "version": "8.57.1",



      "resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz",



      "integrity": "sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      }



    },



    "node_modules/@humanwhocodes/config-array": {



      "version": "0.13.0",



      "resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz",



      "integrity": "sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==",



      "deprecated": "Use @eslint/config-array instead",



      "dev": true,



      "license": "Apache-2.0",



      "dependencies": {



        "@humanwhocodes/object-schema": "^2.0.3",



        "debug": "^4.3.1",



        "minimatch": "^3.0.5"



      },



      "engines": {



        "node": ">=10.10.0"



      }



    },



    "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {



      "version": "1.1.12",



      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",



      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "balanced-match": "^1.0.0",



        "concat-map": "0.0.1"



      }



    },



    "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {



      "version": "3.1.2",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",



      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^1.1.7"



      },



      "engines": {



        "node": "*"



      }



    },



    "node_modules/@humanwhocodes/module-importer": {



      "version": "1.0.1",



      "resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz",



      "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==",



      "dev": true,



      "license": "Apache-2.0",



      "engines": {



        "node": ">=12.22"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/nzakas"



      }



    },



    "node_modules/@humanwhocodes/object-schema": {



      "version": "2.0.3",



      "resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz",



      "integrity": "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==",



      "deprecated": "Use @eslint/object-schema instead",



      "dev": true,



      "license": "BSD-3-Clause"



    },



    "node_modules/@isaacs/cliui": {



      "version": "8.0.2",



      "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz",



      "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "string-width": "^5.1.2",



        "string-width-cjs": "npm:string-width@^4.2.0",



        "strip-ansi": "^7.0.1",



        "strip-ansi-cjs": "npm:strip-ansi@^6.0.1",



        "wrap-ansi": "^8.1.0",



        "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"



      },



      "engines": {



        "node": ">=12"



      }



    },



    "node_modules/@isaacs/cliui/node_modules/ansi-regex": {



      "version": "6.1.0",



      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",



      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/ansi-regex?sponsor=1"



      }



    },



    "node_modules/@isaacs/cliui/node_modules/strip-ansi": {



      "version": "7.1.0",



      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",



      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-regex": "^6.0.1"



      },



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/strip-ansi?sponsor=1"



      }



    },



    "node_modules/@jridgewell/gen-mapping": {



      "version": "0.3.12",



      "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz",



      "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@jridgewell/sourcemap-codec": "^1.5.0",



        "@jridgewell/trace-mapping": "^0.3.24"



      }



    },



    "node_modules/@jridgewell/resolve-uri": {



      "version": "3.1.2",



      "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",



      "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.0.0"



      }



    },



    "node_modules/@jridgewell/sourcemap-codec": {



      "version": "1.5.4",



      "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz",



      "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/@jridgewell/trace-mapping": {



      "version": "0.3.29",



      "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz",



      "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@jridgewell/resolve-uri": "^3.1.0",



        "@jridgewell/sourcemap-codec": "^1.4.14"



      }



    },



    "node_modules/@nodelib/fs.scandir": {



      "version": "2.1.5",



      "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",



      "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@nodelib/fs.stat": "2.0.5",



        "run-parallel": "^1.1.9"



      },



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/@nodelib/fs.stat": {



      "version": "2.0.5",



      "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",



      "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/@nodelib/fs.walk": {



      "version": "1.2.8",



      "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",



      "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@nodelib/fs.scandir": "2.1.5",



        "fastq": "^1.6.0"



      },



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/@pkgjs/parseargs": {



      "version": "0.11.0",



      "resolved": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz",



      "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==",



      "dev": true,



      "license": "MIT",



      "optional": true,



      "engines": {



        "node": ">=14"



      }



    },



    "node_modules/@rolldown/pluginutils": {



      "version": "1.0.0-beta.19",



      "resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz",



      "integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/@rollup/rollup-android-arm-eabi": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.45.1.tgz",



      "integrity": "sha512-NEySIFvMY0ZQO+utJkgoMiCAjMrGvnbDLHvcmlA33UXJpYBCvlBEbMMtV837uCkS+plG2umfhn0T5mMAxGrlRA==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "android"



      ]



    },



    "node_modules/@rollup/rollup-android-arm64": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.45.1.tgz",



      "integrity": "sha512-ujQ+sMXJkg4LRJaYreaVx7Z/VMgBBd89wGS4qMrdtfUFZ+TSY5Rs9asgjitLwzeIbhwdEhyj29zhst3L1lKsRQ==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "android"



      ]



    },



    "node_modules/@rollup/rollup-darwin-arm64": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.45.1.tgz",



      "integrity": "sha512-FSncqHvqTm3lC6Y13xncsdOYfxGSLnP+73k815EfNmpewPs+EyM49haPS105Rh4aF5mJKywk9X0ogzLXZzN9lA==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "darwin"



      ]



    },



    "node_modules/@rollup/rollup-darwin-x64": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.45.1.tgz",



      "integrity": "sha512-2/vVn/husP5XI7Fsf/RlhDaQJ7x9zjvC81anIVbr4b/f0xtSmXQTFcGIQ/B1cXIYM6h2nAhJkdMHTnD7OtQ9Og==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "darwin"



      ]



    },



    "node_modules/@rollup/rollup-freebsd-arm64": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.45.1.tgz",



      "integrity": "sha512-4g1kaDxQItZsrkVTdYQ0bxu4ZIQ32cotoQbmsAnW1jAE4XCMbcBPDirX5fyUzdhVCKgPcrwWuucI8yrVRBw2+g==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "freebsd"



      ]



    },



    "node_modules/@rollup/rollup-freebsd-x64": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.45.1.tgz",



      "integrity": "sha512-L/6JsfiL74i3uK1Ti2ZFSNsp5NMiM4/kbbGEcOCps99aZx3g8SJMO1/9Y0n/qKlWZfn6sScf98lEOUe2mBvW9A==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "freebsd"



      ]



    },



    "node_modules/@rollup/rollup-linux-arm-gnueabihf": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.45.1.tgz",



      "integrity": "sha512-RkdOTu2jK7brlu+ZwjMIZfdV2sSYHK2qR08FUWcIoqJC2eywHbXr0L8T/pONFwkGukQqERDheaGTeedG+rra6Q==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-arm-musleabihf": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.45.1.tgz",



      "integrity": "sha512-3kJ8pgfBt6CIIr1o+HQA7OZ9mp/zDk3ctekGl9qn/pRBgrRgfwiffaUmqioUGN9hv0OHv2gxmvdKOkARCtRb8Q==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-arm64-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.45.1.tgz",



      "integrity": "sha512-k3dOKCfIVixWjG7OXTCOmDfJj3vbdhN0QYEqB+OuGArOChek22hn7Uy5A/gTDNAcCy5v2YcXRJ/Qcnm4/ma1xw==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-arm64-musl": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.45.1.tgz",



      "integrity": "sha512-PmI1vxQetnM58ZmDFl9/Uk2lpBBby6B6rF4muJc65uZbxCs0EA7hhKCk2PKlmZKuyVSHAyIw3+/SiuMLxKxWog==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-loongarch64-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.45.1.tgz",



      "integrity": "sha512-9UmI0VzGmNJ28ibHW2GpE2nF0PBQqsyiS4kcJ5vK+wuwGnV5RlqdczVocDSUfGX/Na7/XINRVoUgJyFIgipoRg==",



      "cpu": [



        "loong64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.45.1.tgz",



      "integrity": "sha512-7nR2KY8oEOUTD3pBAxIBBbZr0U7U+R9HDTPNy+5nVVHDXI4ikYniH1oxQz9VoB5PbBU1CZuDGHkLJkd3zLMWsg==",



      "cpu": [



        "ppc64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-riscv64-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.45.1.tgz",



      "integrity": "sha512-nlcl3jgUultKROfZijKjRQLUu9Ma0PeNv/VFHkZiKbXTBQXhpytS8CIj5/NfBeECZtY2FJQubm6ltIxm/ftxpw==",



      "cpu": [



        "riscv64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-riscv64-musl": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.45.1.tgz",



      "integrity": "sha512-HJV65KLS51rW0VY6rvZkiieiBnurSzpzore1bMKAhunQiECPuxsROvyeaot/tcK3A3aGnI+qTHqisrpSgQrpgA==",



      "cpu": [



        "riscv64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-s390x-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.45.1.tgz",



      "integrity": "sha512-NITBOCv3Qqc6hhwFt7jLV78VEO/il4YcBzoMGGNxznLgRQf43VQDae0aAzKiBeEPIxnDrACiMgbqjuihx08OOw==",



      "cpu": [



        "s390x"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-x64-gnu": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.45.1.tgz",



      "integrity": "sha512-+E/lYl6qu1zqgPEnTrs4WysQtvc/Sh4fC2nByfFExqgYrqkKWp1tWIbe+ELhixnenSpBbLXNi6vbEEJ8M7fiHw==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-linux-x64-musl": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.45.1.tgz",



      "integrity": "sha512-a6WIAp89p3kpNoYStITT9RbTbTnqarU7D8N8F2CV+4Cl9fwCOZraLVuVFvlpsW0SbIiYtEnhCZBPLoNdRkjQFw==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "linux"



      ]



    },



    "node_modules/@rollup/rollup-win32-arm64-msvc": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.45.1.tgz",



      "integrity": "sha512-T5Bi/NS3fQiJeYdGvRpTAP5P02kqSOpqiopwhj0uaXB6nzs5JVi2XMJb18JUSKhCOX8+UE1UKQufyD6Or48dJg==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ]



    },



    "node_modules/@rollup/rollup-win32-ia32-msvc": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.45.1.tgz",



      "integrity": "sha512-lxV2Pako3ujjuUe9jiU3/s7KSrDfH6IgTSQOnDWr9aJ92YsFd7EurmClK0ly/t8dzMkDtd04g60WX6yl0sGfdw==",



      "cpu": [



        "ia32"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ]



    },



    "node_modules/@rollup/rollup-win32-x64-msvc": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.45.1.tgz",



      "integrity": "sha512-M/fKi4sasCdM8i0aWJjCSFm2qEnYRR8AMLG2kxp6wD13+tMGA4Z1tVAuHkNRjud5SW2EM3naLuK35w9twvf6aA==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "win32"



      ]



    },



    "node_modules/@tauri-apps/api": {



      "version": "2.6.0",



      "resolved": "https://registry.npmjs.org/@tauri-apps/api/-/api-2.6.0.tgz",



      "integrity": "sha512-hRNcdercfgpzgFrMXWwNDBN0B7vNzOzRepy6ZAmhxi5mDLVPNrTpo9MGg2tN/F7JRugj4d2aF7E1rtPXAHaetg==",



      "license": "Apache-2.0 OR MIT",



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/tauri"



      }



    },



    "node_modules/@tauri-apps/cli": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-2.6.2.tgz",



      "integrity": "sha512-s1/eyBHxk0wG1blLeOY2IDjgZcxVrkxU5HFL8rNDwjYGr0o7yr3RAtwmuUPhz13NO+xGAL1bJZaLFBdp+5joKg==",



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "bin": {



        "tauri": "tauri.js"



      },



      "engines": {



        "node": ">= 10"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/tauri"



      },



      "optionalDependencies": {



        "@tauri-apps/cli-darwin-arm64": "2.6.2",



        "@tauri-apps/cli-darwin-x64": "2.6.2",



        "@tauri-apps/cli-linux-arm-gnueabihf": "2.6.2",



        "@tauri-apps/cli-linux-arm64-gnu": "2.6.2",



        "@tauri-apps/cli-linux-arm64-musl": "2.6.2",



        "@tauri-apps/cli-linux-riscv64-gnu": "2.6.2",



        "@tauri-apps/cli-linux-x64-gnu": "2.6.2",



        "@tauri-apps/cli-linux-x64-musl": "2.6.2",



        "@tauri-apps/cli-win32-arm64-msvc": "2.6.2",



        "@tauri-apps/cli-win32-ia32-msvc": "2.6.2",



        "@tauri-apps/cli-win32-x64-msvc": "2.6.2"



      }



    },



    "node_modules/@tauri-apps/cli-darwin-arm64": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-2.6.2.tgz",



      "integrity": "sha512-YlvT+Yb7u2HplyN2Cf/nBplCQARC/I4uedlYHlgtxg6rV7xbo9BvG1jLOo29IFhqA2rOp5w1LtgvVGwsOf2kxw==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "darwin"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-darwin-x64": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-2.6.2.tgz",



      "integrity": "sha512-21gdPWfv1bP8rkTdCL44in70QcYcPaDM70L+y78N8TkBuC+/+wqnHcwwjzb+mUyck6UoEw2DORagSI/oKKUGJw==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "darwin"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-arm-gnueabihf": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-2.6.2.tgz",



      "integrity": "sha512-MW8Y6HqHS5yzQkwGoLk/ZyE1tWpnz/seDoY4INsbvUZdknuUf80yn3H+s6eGKtT/0Bfqon/W9sY7pEkgHRPQgA==",



      "cpu": [



        "arm"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-arm64-gnu": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-2.6.2.tgz",



      "integrity": "sha512-9PdINTUtnyrnQt9hvC4y1m0NoxKSw/wUB9OTBAQabPj8WLAdvySWiUpEiqJjwLhlu4T6ltXZRpNTEzous3/RXg==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-arm64-musl": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-2.6.2.tgz",



      "integrity": "sha512-LrcJTRr7FrtQlTDkYaRXIGo/8YU/xkWmBPC646WwKNZ/S6yqCiDcOMoPe7Cx4ZvcG6sK6LUCLQMfaSNEL7PT0A==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-riscv64-gnu": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-riscv64-gnu/-/cli-linux-riscv64-gnu-2.6.2.tgz",



      "integrity": "sha512-GnTshO/BaZ9KGIazz2EiFfXGWgLur5/pjqklRA/ck42PGdUQJhV/Ao7A7TdXPjqAzpFxNo6M/Hx0GCH2iMS7IA==",



      "cpu": [



        "riscv64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-x64-gnu": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-2.6.2.tgz",



      "integrity": "sha512-QDG3WeJD6UJekmrtVPCJRzlKgn9sGzhvD58oAw5gIU+DRovgmmG2U1jH9fS361oYGjWWO7d/KM9t0kugZzi4lQ==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-linux-x64-musl": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-2.6.2.tgz",



      "integrity": "sha512-TNVTDDtnWzuVqWBFdZ4+8ZTg17tc21v+CT5XBQ+KYCoYtCrIaHpW04fS5Tmudi+vYdBwoPDfwpKEB6LhCeFraQ==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "linux"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-win32-arm64-msvc": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-arm64-msvc/-/cli-win32-arm64-msvc-2.6.2.tgz",



      "integrity": "sha512-z77C1oa/hMLO/jM1JF39tK3M3v9nou7RsBnQoOY54z5WPcpVAbS0XdFhXB7sSN72BOiO3moDky9lQANQz6L3CA==",



      "cpu": [



        "arm64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-win32-ia32-msvc": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-2.6.2.tgz",



      "integrity": "sha512-TmD8BbzbjluBw8+QEIWUVmFa9aAluSkT1N937n1mpYLXcPbTpbunqRFiIznTwupoJNJIdtpF/t7BdZDRh5rrcg==",



      "cpu": [



        "ia32"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/cli-win32-x64-msvc": {



      "version": "2.6.2",



      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-2.6.2.tgz",



      "integrity": "sha512-ItB8RCKk+nCmqOxOvbNtltz6x1A4QX6cSM21kj3NkpcnjT9rHSMcfyf8WVI2fkoMUJR80iqCblUX6ARxC3lj6w==",



      "cpu": [



        "x64"



      ],



      "dev": true,



      "license": "Apache-2.0 OR MIT",



      "optional": true,



      "os": [



        "win32"



      ],



      "engines": {



        "node": ">= 10"



      }



    },



    "node_modules/@tauri-apps/plugin-fs": {



      "version": "2.4.0",



      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-fs/-/plugin-fs-2.4.0.tgz",



      "integrity": "sha512-Sp8AdDcbyXyk6LD6Pmdx44SH3LPeNAvxR2TFfq/8CwqzfO1yOyV+RzT8fov0NNN7d9nvW7O7MtMAptJ42YXA5g==",



      "license": "MIT OR Apache-2.0",



      "dependencies": {



        "@tauri-apps/api": "^2.6.0"



      }



    },



    "node_modules/@tauri-apps/plugin-http": {



      "version": "2.5.0",



      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-http/-/plugin-http-2.5.0.tgz",



      "integrity": "sha512-l4M2DUIsOBIMrbj4dJZwrB4mJiB7OA/2Tj3gEbX2fjq5MOpETklJPKfDvzUTDwuq4lIKCKKykz8E8tpOgvi0EQ==",



      "license": "MIT OR Apache-2.0",



      "dependencies": {



        "@tauri-apps/api": "^2.6.0"



      }



    },



    "node_modules/@tauri-apps/plugin-shell": {



      "version": "2.3.0",



      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-shell/-/plugin-shell-2.3.0.tgz",



      "integrity": "sha512-6GIRxO2z64uxPX4CCTuhQzefvCC0ew7HjdBhMALiGw74vFBDY95VWueAHOHgNOMV4UOUAFupyidN9YulTe5xlA==",



      "license": "MIT OR Apache-2.0",



      "dependencies": {



        "@tauri-apps/api": "^2.6.0"



      }



    },



    "node_modules/@types/babel__core": {



      "version": "7.20.5",



      "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz",



      "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/parser": "^7.20.7",



        "@babel/types": "^7.20.7",



        "@types/babel__generator": "*",



        "@types/babel__template": "*",



        "@types/babel__traverse": "*"



      }



    },



    "node_modules/@types/babel__generator": {



      "version": "7.27.0",



      "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz",



      "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/types": "^7.0.0"



      }



    },



    "node_modules/@types/babel__template": {



      "version": "7.4.4",



      "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz",



      "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/parser": "^7.1.0",



        "@babel/types": "^7.0.0"



      }



    },



    "node_modules/@types/babel__traverse": {



      "version": "7.20.7",



      "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz",



      "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/types": "^7.20.7"



      }



    },



    "node_modules/@types/debug": {



      "version": "4.1.12",



      "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz",



      "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==",



      "license": "MIT",



      "dependencies": {



        "@types/ms": "*"



      }



    },



    "node_modules/@types/estree": {



      "version": "1.0.8",



      "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz",



      "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==",



      "license": "MIT"



    },



    "node_modules/@types/estree-jsx": {



      "version": "1.0.5",



      "resolved": "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz",



      "integrity": "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",



      "license": "MIT",



      "dependencies": {



        "@types/estree": "*"



      }



    },



    "node_modules/@types/hast": {



      "version": "3.0.4",



      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz",



      "integrity": "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "*"



      }



    },



    "node_modules/@types/json-schema": {



      "version": "7.0.15",



      "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz",



      "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/@types/mdast": {



      "version": "4.0.4",



      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz",



      "integrity": "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "*"



      }



    },



    "node_modules/@types/ms": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz",



      "integrity": "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==",



      "license": "MIT"



    },



    "node_modules/@types/prop-types": {



      "version": "15.7.15",



      "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz",



      "integrity": "sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==",



      "license": "MIT"



    },



    "node_modules/@types/react": {



      "version": "18.3.23",



      "resolved": "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz",



      "integrity": "sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==",



      "license": "MIT",



      "dependencies": {



        "@types/prop-types": "*",



        "csstype": "^3.0.2"



      }



    },



    "node_modules/@types/react-dom": {



      "version": "18.3.7",



      "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz",



      "integrity": "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==",



      "dev": true,



      "license": "MIT",



      "peerDependencies": {



        "@types/react": "^18.0.0"



      }



    },



    "node_modules/@types/semver": {



      "version": "7.7.0",



      "resolved": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz",



      "integrity": "sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/@types/unist": {



      "version": "3.0.3",



      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz",



      "integrity": "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==",



      "license": "MIT"



    },



    "node_modules/@typescript-eslint/eslint-plugin": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz",



      "integrity": "sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@eslint-community/regexpp": "^4.5.1",



        "@typescript-eslint/scope-manager": "6.21.0",



        "@typescript-eslint/type-utils": "6.21.0",



        "@typescript-eslint/utils": "6.21.0",



        "@typescript-eslint/visitor-keys": "6.21.0",



        "debug": "^4.3.4",



        "graphemer": "^1.4.0",



        "ignore": "^5.2.4",



        "natural-compare": "^1.4.0",



        "semver": "^7.5.4",



        "ts-api-utils": "^1.0.1"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      },



      "peerDependencies": {



        "@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha",



        "eslint": "^7.0.0 || ^8.0.0"



      },



      "peerDependenciesMeta": {



        "typescript": {



          "optional": true



        }



      }



    },



    "node_modules/@typescript-eslint/parser": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.21.0.tgz",



      "integrity": "sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "@typescript-eslint/scope-manager": "6.21.0",



        "@typescript-eslint/types": "6.21.0",



        "@typescript-eslint/typescript-estree": "6.21.0",



        "@typescript-eslint/visitor-keys": "6.21.0",



        "debug": "^4.3.4"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      },



      "peerDependencies": {



        "eslint": "^7.0.0 || ^8.0.0"



      },



      "peerDependenciesMeta": {



        "typescript": {



          "optional": true



        }



      }



    },



    "node_modules/@typescript-eslint/scope-manager": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz",



      "integrity": "sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@typescript-eslint/types": "6.21.0",



        "@typescript-eslint/visitor-keys": "6.21.0"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      }



    },



    "node_modules/@typescript-eslint/type-utils": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz",



      "integrity": "sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@typescript-eslint/typescript-estree": "6.21.0",



        "@typescript-eslint/utils": "6.21.0",



        "debug": "^4.3.4",



        "ts-api-utils": "^1.0.1"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      },



      "peerDependencies": {



        "eslint": "^7.0.0 || ^8.0.0"



      },



      "peerDependenciesMeta": {



        "typescript": {



          "optional": true



        }



      }



    },



    "node_modules/@typescript-eslint/types": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/types/-/types-6.21.0.tgz",



      "integrity": "sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      }



    },



    "node_modules/@typescript-eslint/typescript-estree": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz",



      "integrity": "sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "@typescript-eslint/types": "6.21.0",



        "@typescript-eslint/visitor-keys": "6.21.0",



        "debug": "^4.3.4",



        "globby": "^11.1.0",



        "is-glob": "^4.0.3",



        "minimatch": "9.0.3",



        "semver": "^7.5.4",



        "ts-api-utils": "^1.0.1"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      },



      "peerDependenciesMeta": {



        "typescript": {



          "optional": true



        }



      }



    },



    "node_modules/@typescript-eslint/utils": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-6.21.0.tgz",



      "integrity": "sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@eslint-community/eslint-utils": "^4.4.0",



        "@types/json-schema": "^7.0.12",



        "@types/semver": "^7.5.0",



        "@typescript-eslint/scope-manager": "6.21.0",



        "@typescript-eslint/types": "6.21.0",



        "@typescript-eslint/typescript-estree": "6.21.0",



        "semver": "^7.5.4"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      },



      "peerDependencies": {



        "eslint": "^7.0.0 || ^8.0.0"



      }



    },



    "node_modules/@typescript-eslint/visitor-keys": {



      "version": "6.21.0",



      "resolved": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz",



      "integrity": "sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@typescript-eslint/types": "6.21.0",



        "eslint-visitor-keys": "^3.4.1"



      },



      "engines": {



        "node": "^16.0.0 || >=18.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/typescript-eslint"



      }



    },



    "node_modules/@ungap/structured-clone": {



      "version": "1.3.0",



      "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz",



      "integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==",



      "license": "ISC"



    },



    "node_modules/@vitejs/plugin-react": {



      "version": "4.6.0",



      "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz",



      "integrity": "sha512-5Kgff+m8e2PB+9j51eGHEpn5kUzRKH2Ry0qGoe8ItJg7pqnkPrYPkDQZGgGmTa0EGarHrkjLvOdU3b1fzI8otQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@babel/core": "^7.27.4",



        "@babel/plugin-transform-react-jsx-self": "^7.27.1",



        "@babel/plugin-transform-react-jsx-source": "^7.27.1",



        "@rolldown/pluginutils": "1.0.0-beta.19",



        "@types/babel__core": "^7.20.5",



        "react-refresh": "^0.17.0"



      },



      "engines": {



        "node": "^14.18.0 || >=16.0.0"



      },



      "peerDependencies": {



        "vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"



      }



    },



    "node_modules/acorn": {



      "version": "8.15.0",



      "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz",



      "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "acorn": "bin/acorn"



      },



      "engines": {



        "node": ">=0.4.0"



      }



    },



    "node_modules/acorn-jsx": {



      "version": "5.3.2",



      "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz",



      "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",



      "dev": true,



      "license": "MIT",



      "peerDependencies": {



        "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"



      }



    },



    "node_modules/ajv": {



      "version": "6.12.6",



      "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz",



      "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "fast-deep-equal": "^3.1.1",



        "fast-json-stable-stringify": "^2.0.0",



        "json-schema-traverse": "^0.4.1",



        "uri-js": "^4.2.2"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/epoberezkin"



      }



    },



    "node_modules/ansi-regex": {



      "version": "5.0.1",



      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",



      "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/ansi-styles": {



      "version": "4.3.0",



      "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz",



      "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "color-convert": "^2.0.1"



      },



      "engines": {



        "node": ">=8"



      },



      "funding": {



        "url": "https://github.com/chalk/ansi-styles?sponsor=1"



      }



    },



    "node_modules/any-promise": {



      "version": "1.3.0",



      "resolved": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz",



      "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/anymatch": {



      "version": "3.1.3",



      "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",



      "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "normalize-path": "^3.0.0",



        "picomatch": "^2.0.4"



      },



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/arg": {



      "version": "5.0.2",



      "resolved": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz",



      "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/argparse": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",



      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",



      "dev": true,



      "license": "Python-2.0"



    },



    "node_modules/array-union": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz",



      "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/autoprefixer": {



      "version": "10.4.21",



      "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz",



      "integrity": "sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/postcss/"



        },



        {



          "type": "tidelift",



          "url": "https://tidelift.com/funding/github/npm/autoprefixer"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "browserslist": "^4.24.4",



        "caniuse-lite": "^1.0.30001702",



        "fraction.js": "^4.3.7",



        "normalize-range": "^0.1.2",



        "picocolors": "^1.1.1",



        "postcss-value-parser": "^4.2.0"



      },



      "bin": {



        "autoprefixer": "bin/autoprefixer"



      },



      "engines": {



        "node": "^10 || ^12 || >=14"



      },



      "peerDependencies": {



        "postcss": "^8.1.0"



      }



    },



    "node_modules/bail": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz",



      "integrity": "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/balanced-match": {



      "version": "1.0.2",



      "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",



      "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/binary-extensions": {



      "version": "2.3.0",



      "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz",



      "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/brace-expansion": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz",



      "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "balanced-match": "^1.0.0"



      }



    },



    "node_modules/braces": {



      "version": "3.0.3",



      "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",



      "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "fill-range": "^7.1.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/browserslist": {



      "version": "4.25.1",



      "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz",



      "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/browserslist"



        },



        {



          "type": "tidelift",



          "url": "https://tidelift.com/funding/github/npm/browserslist"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "caniuse-lite": "^1.0.30001726",



        "electron-to-chromium": "^1.5.173",



        "node-releases": "^2.0.19",



        "update-browserslist-db": "^1.1.3"



      },



      "bin": {



        "browserslist": "cli.js"



      },



      "engines": {



        "node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"



      }



    },



    "node_modules/callsites": {



      "version": "3.1.0",



      "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz",



      "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/camelcase-css": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz",



      "integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/caniuse-lite": {



      "version": "1.0.30001727",



      "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz",



      "integrity": "sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/browserslist"



        },



        {



          "type": "tidelift",



          "url": "https://tidelift.com/funding/github/npm/caniuse-lite"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "CC-BY-4.0"



    },



    "node_modules/ccount": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz",



      "integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/chalk": {



      "version": "4.1.2",



      "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",



      "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-styles": "^4.1.0",



        "supports-color": "^7.1.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/chalk/chalk?sponsor=1"



      }



    },



    "node_modules/character-entities": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz",



      "integrity": "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/character-entities-html4": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz",



      "integrity": "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/character-entities-legacy": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz",



      "integrity": "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/character-reference-invalid": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz",



      "integrity": "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/chokidar": {



      "version": "3.6.0",



      "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz",



      "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "anymatch": "~3.1.2",



        "braces": "~3.0.2",



        "glob-parent": "~5.1.2",



        "is-binary-path": "~2.1.0",



        "is-glob": "~4.0.1",



        "normalize-path": "~3.0.0",



        "readdirp": "~3.6.0"



      },



      "engines": {



        "node": ">= 8.10.0"



      },



      "funding": {



        "url": "https://paulmillr.com/funding/"



      },



      "optionalDependencies": {



        "fsevents": "~2.3.2"



      }



    },



    "node_modules/chokidar/node_modules/glob-parent": {



      "version": "5.1.2",



      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",



      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "is-glob": "^4.0.1"



      },



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/clsx": {



      "version": "2.1.1",



      "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",



      "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",



      "license": "MIT",



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/color-convert": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",



      "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "color-name": "~1.1.4"



      },



      "engines": {



        "node": ">=7.0.0"



      }



    },



    "node_modules/color-name": {



      "version": "1.1.4",



      "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",



      "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/comma-separated-tokens": {



      "version": "2.0.3",



      "resolved": "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz",



      "integrity": "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/commander": {



      "version": "4.1.1",



      "resolved": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz",



      "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/concat-map": {



      "version": "0.0.1",



      "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz",



      "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/convert-source-map": {



      "version": "2.0.0",



      "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz",



      "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/cross-spawn": {



      "version": "7.0.6",



      "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",



      "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "path-key": "^3.1.0",



        "shebang-command": "^2.0.0",



        "which": "^2.0.1"



      },



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/cssesc": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz",



      "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "cssesc": "bin/cssesc"



      },



      "engines": {



        "node": ">=4"



      }



    },



    "node_modules/csstype": {



      "version": "3.1.3",



      "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz",



      "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==",



      "license": "MIT"



    },



    "node_modules/debug": {



      "version": "4.4.1",



      "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz",



      "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==",



      "license": "MIT",



      "dependencies": {



        "ms": "^2.1.3"



      },



      "engines": {



        "node": ">=6.0"



      },



      "peerDependenciesMeta": {



        "supports-color": {



          "optional": true



        }



      }



    },



    "node_modules/decode-named-character-reference": {



      "version": "1.2.0",



      "resolved": "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.2.0.tgz",



      "integrity": "sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==",



      "license": "MIT",



      "dependencies": {



        "character-entities": "^2.0.0"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/deep-is": {



      "version": "0.1.4",



      "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz",



      "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/dequal": {



      "version": "2.0.3",



      "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz",



      "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==",



      "license": "MIT",



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/devlop": {



      "version": "1.1.0",



      "resolved": "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz",



      "integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",



      "license": "MIT",



      "dependencies": {



        "dequal": "^2.0.0"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/didyoumean": {



      "version": "1.2.2",



      "resolved": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz",



      "integrity": "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==",



      "dev": true,



      "license": "Apache-2.0"



    },



    "node_modules/dir-glob": {



      "version": "3.0.1",



      "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz",



      "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "path-type": "^4.0.0"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/dlv": {



      "version": "1.1.3",



      "resolved": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz",



      "integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/doctrine": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz",



      "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==",



      "dev": true,



      "license": "Apache-2.0",



      "dependencies": {



        "esutils": "^2.0.2"



      },



      "engines": {



        "node": ">=6.0.0"



      }



    },



    "node_modules/eastasianwidth": {



      "version": "0.2.0",



      "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz",



      "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/electron-to-chromium": {



      "version": "1.5.186",



      "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.186.tgz",



      "integrity": "sha512-lur7L4BFklgepaJxj4DqPk7vKbTEl0pajNlg2QjE5shefmlmBLm2HvQ7PMf1R/GvlevT/581cop33/quQcfX3A==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/emoji-regex": {



      "version": "9.2.2",



      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz",



      "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/esbuild": {



      "version": "0.21.5",



      "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.21.5.tgz",



      "integrity": "sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==",



      "dev": true,



      "hasInstallScript": true,



      "license": "MIT",



      "bin": {



        "esbuild": "bin/esbuild"



      },



      "engines": {



        "node": ">=12"



      },



      "optionalDependencies": {



        "@esbuild/aix-ppc64": "0.21.5",



        "@esbuild/android-arm": "0.21.5",



        "@esbuild/android-arm64": "0.21.5",



        "@esbuild/android-x64": "0.21.5",



        "@esbuild/darwin-arm64": "0.21.5",



        "@esbuild/darwin-x64": "0.21.5",



        "@esbuild/freebsd-arm64": "0.21.5",



        "@esbuild/freebsd-x64": "0.21.5",



        "@esbuild/linux-arm": "0.21.5",



        "@esbuild/linux-arm64": "0.21.5",



        "@esbuild/linux-ia32": "0.21.5",



        "@esbuild/linux-loong64": "0.21.5",



        "@esbuild/linux-mips64el": "0.21.5",



        "@esbuild/linux-ppc64": "0.21.5",



        "@esbuild/linux-riscv64": "0.21.5",



        "@esbuild/linux-s390x": "0.21.5",



        "@esbuild/linux-x64": "0.21.5",



        "@esbuild/netbsd-x64": "0.21.5",



        "@esbuild/openbsd-x64": "0.21.5",



        "@esbuild/sunos-x64": "0.21.5",



        "@esbuild/win32-arm64": "0.21.5",



        "@esbuild/win32-ia32": "0.21.5",



        "@esbuild/win32-x64": "0.21.5"



      }



    },



    "node_modules/escalade": {



      "version": "3.2.0",



      "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz",



      "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/escape-string-regexp": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz",



      "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/eslint": {



      "version": "8.57.1",



      "resolved": "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz",



      "integrity": "sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==",



      "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@eslint-community/eslint-utils": "^4.2.0",



        "@eslint-community/regexpp": "^4.6.1",



        "@eslint/eslintrc": "^2.1.4",



        "@eslint/js": "8.57.1",



        "@humanwhocodes/config-array": "^0.13.0",



        "@humanwhocodes/module-importer": "^1.0.1",



        "@nodelib/fs.walk": "^1.2.8",



        "@ungap/structured-clone": "^1.2.0",



        "ajv": "^6.12.4",



        "chalk": "^4.0.0",



        "cross-spawn": "^7.0.2",



        "debug": "^4.3.2",



        "doctrine": "^3.0.0",



        "escape-string-regexp": "^4.0.0",



        "eslint-scope": "^7.2.2",



        "eslint-visitor-keys": "^3.4.3",



        "espree": "^9.6.1",



        "esquery": "^1.4.2",



        "esutils": "^2.0.2",



        "fast-deep-equal": "^3.1.3",



        "file-entry-cache": "^6.0.1",



        "find-up": "^5.0.0",



        "glob-parent": "^6.0.2",



        "globals": "^13.19.0",



        "graphemer": "^1.4.0",



        "ignore": "^5.2.0",



        "imurmurhash": "^0.1.4",



        "is-glob": "^4.0.0",



        "is-path-inside": "^3.0.3",



        "js-yaml": "^4.1.0",



        "json-stable-stringify-without-jsonify": "^1.0.1",



        "levn": "^0.4.1",



        "lodash.merge": "^4.6.2",



        "minimatch": "^3.1.2",



        "natural-compare": "^1.4.0",



        "optionator": "^0.9.3",



        "strip-ansi": "^6.0.1",



        "text-table": "^0.2.0"



      },



      "bin": {



        "eslint": "bin/eslint.js"



      },



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      }



    },



    "node_modules/eslint-plugin-react-hooks": {



      "version": "4.6.2",



      "resolved": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz",



      "integrity": "sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=10"



      },



      "peerDependencies": {



        "eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"



      }



    },



    "node_modules/eslint-plugin-react-refresh": {



      "version": "0.4.20",



      "resolved": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz",



      "integrity": "sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==",



      "dev": true,



      "license": "MIT",



      "peerDependencies": {



        "eslint": ">=8.40"



      }



    },



    "node_modules/eslint-scope": {



      "version": "7.2.2",



      "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz",



      "integrity": "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "esrecurse": "^4.3.0",



        "estraverse": "^5.2.0"



      },



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      }



    },



    "node_modules/eslint-visitor-keys": {



      "version": "3.4.3",



      "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz",



      "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==",



      "dev": true,



      "license": "Apache-2.0",



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      }



    },



    "node_modules/eslint/node_modules/brace-expansion": {



      "version": "1.1.12",



      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",



      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "balanced-match": "^1.0.0",



        "concat-map": "0.0.1"



      }



    },



    "node_modules/eslint/node_modules/minimatch": {



      "version": "3.1.2",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",



      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^1.1.7"



      },



      "engines": {



        "node": "*"



      }



    },



    "node_modules/espree": {



      "version": "9.6.1",



      "resolved": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz",



      "integrity": "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "acorn": "^8.9.0",



        "acorn-jsx": "^5.3.2",



        "eslint-visitor-keys": "^3.4.1"



      },



      "engines": {



        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"



      },



      "funding": {



        "url": "https://opencollective.com/eslint"



      }



    },



    "node_modules/esquery": {



      "version": "1.6.0",



      "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz",



      "integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==",



      "dev": true,



      "license": "BSD-3-Clause",



      "dependencies": {



        "estraverse": "^5.1.0"



      },



      "engines": {



        "node": ">=0.10"



      }



    },



    "node_modules/esrecurse": {



      "version": "4.3.0",



      "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz",



      "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "estraverse": "^5.2.0"



      },



      "engines": {



        "node": ">=4.0"



      }



    },



    "node_modules/estraverse": {



      "version": "5.3.0",



      "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz",



      "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==",



      "dev": true,



      "license": "BSD-2-Clause",



      "engines": {



        "node": ">=4.0"



      }



    },



    "node_modules/estree-util-is-identifier-name": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz",



      "integrity": "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==",



      "license": "MIT",



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/esutils": {



      "version": "2.0.3",



      "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz",



      "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==",



      "dev": true,



      "license": "BSD-2-Clause",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/extend": {



      "version": "3.0.2",



      "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz",



      "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==",



      "license": "MIT"



    },



    "node_modules/fast-deep-equal": {



      "version": "3.1.3",



      "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz",



      "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/fast-glob": {



      "version": "3.3.3",



      "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",



      "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@nodelib/fs.stat": "^2.0.2",



        "@nodelib/fs.walk": "^1.2.3",



        "glob-parent": "^5.1.2",



        "merge2": "^1.3.0",



        "micromatch": "^4.0.8"



      },



      "engines": {



        "node": ">=8.6.0"



      }



    },



    "node_modules/fast-glob/node_modules/glob-parent": {



      "version": "5.1.2",



      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",



      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "is-glob": "^4.0.1"



      },



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/fast-json-stable-stringify": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz",



      "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/fast-levenshtein": {



      "version": "2.0.6",



      "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz",



      "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/fastq": {



      "version": "1.19.1",



      "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz",



      "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "reusify": "^1.0.4"



      }



    },



    "node_modules/file-entry-cache": {



      "version": "6.0.1",



      "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz",



      "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "flat-cache": "^3.0.4"



      },



      "engines": {



        "node": "^10.12.0 || >=12.0.0"



      }



    },



    "node_modules/fill-range": {



      "version": "7.1.1",



      "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",



      "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "to-regex-range": "^5.0.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/find-up": {



      "version": "5.0.0",



      "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz",



      "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "locate-path": "^6.0.0",



        "path-exists": "^4.0.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/flat-cache": {



      "version": "3.2.0",



      "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz",



      "integrity": "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "flatted": "^3.2.9",



        "keyv": "^4.5.3",



        "rimraf": "^3.0.2"



      },



      "engines": {



        "node": "^10.12.0 || >=12.0.0"



      }



    },



    "node_modules/flatted": {



      "version": "3.3.3",



      "resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz",



      "integrity": "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/foreground-child": {



      "version": "3.3.1",



      "resolved": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz",



      "integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "cross-spawn": "^7.0.6",



        "signal-exit": "^4.0.1"



      },



      "engines": {



        "node": ">=14"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/fraction.js": {



      "version": "4.3.7",



      "resolved": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz",



      "integrity": "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": "*"



      },



      "funding": {



        "type": "patreon",



        "url": "https://github.com/sponsors/rawify"



      }



    },



    "node_modules/fs.realpath": {



      "version": "1.0.0",



      "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz",



      "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/fsevents": {



      "version": "2.3.3",



      "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",



      "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",



      "dev": true,



      "hasInstallScript": true,



      "license": "MIT",



      "optional": true,



      "os": [



        "darwin"



      ],



      "engines": {



        "node": "^8.16.0 || ^10.6.0 || >=11.0.0"



      }



    },



    "node_modules/function-bind": {



      "version": "1.1.2",



      "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",



      "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",



      "dev": true,



      "license": "MIT",



      "funding": {



        "url": "https://github.com/sponsors/ljharb"



      }



    },



    "node_modules/gensync": {



      "version": "1.0.0-beta.2",



      "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz",



      "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6.9.0"



      }



    },



    "node_modules/glob": {



      "version": "7.2.3",



      "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz",



      "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==",



      "deprecated": "Glob versions prior to v9 are no longer supported",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "fs.realpath": "^1.0.0",



        "inflight": "^1.0.4",



        "inherits": "2",



        "minimatch": "^3.1.1",



        "once": "^1.3.0",



        "path-is-absolute": "^1.0.0"



      },



      "engines": {



        "node": "*"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/glob-parent": {



      "version": "6.0.2",



      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz",



      "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "is-glob": "^4.0.3"



      },



      "engines": {



        "node": ">=10.13.0"



      }



    },



    "node_modules/glob/node_modules/brace-expansion": {



      "version": "1.1.12",



      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",



      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "balanced-match": "^1.0.0",



        "concat-map": "0.0.1"



      }



    },



    "node_modules/glob/node_modules/minimatch": {



      "version": "3.1.2",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",



      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^1.1.7"



      },



      "engines": {



        "node": "*"



      }



    },



    "node_modules/globals": {



      "version": "13.24.0",



      "resolved": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz",



      "integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "type-fest": "^0.20.2"



      },



      "engines": {



        "node": ">=8"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/globby": {



      "version": "11.1.0",



      "resolved": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz",



      "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "array-union": "^2.1.0",



        "dir-glob": "^3.0.1",



        "fast-glob": "^3.2.9",



        "ignore": "^5.2.0",



        "merge2": "^1.4.1",



        "slash": "^3.0.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/graphemer": {



      "version": "1.4.0",



      "resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz",



      "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/has-flag": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz",



      "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/hasown": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",



      "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "function-bind": "^1.1.2"



      },



      "engines": {



        "node": ">= 0.4"



      }



    },



    "node_modules/hast-util-to-jsx-runtime": {



      "version": "2.3.6",



      "resolved": "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz",



      "integrity": "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==",



      "license": "MIT",



      "dependencies": {



        "@types/estree": "^1.0.0",



        "@types/hast": "^3.0.0",



        "@types/unist": "^3.0.0",



        "comma-separated-tokens": "^2.0.0",



        "devlop": "^1.0.0",



        "estree-util-is-identifier-name": "^3.0.0",



        "hast-util-whitespace": "^3.0.0",



        "mdast-util-mdx-expression": "^2.0.0",



        "mdast-util-mdx-jsx": "^3.0.0",



        "mdast-util-mdxjs-esm": "^2.0.0",



        "property-information": "^7.0.0",



        "space-separated-tokens": "^2.0.0",



        "style-to-js": "^1.0.0",



        "unist-util-position": "^5.0.0",



        "vfile-message": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/hast-util-whitespace": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz",



      "integrity": "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",



      "license": "MIT",



      "dependencies": {



        "@types/hast": "^3.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/html-url-attributes": {



      "version": "3.0.1",



      "resolved": "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz",



      "integrity": "sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==",



      "license": "MIT",



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/ignore": {



      "version": "5.3.2",



      "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz",



      "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 4"



      }



    },



    "node_modules/import-fresh": {



      "version": "3.3.1",



      "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz",



      "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "parent-module": "^1.0.0",



        "resolve-from": "^4.0.0"



      },



      "engines": {



        "node": ">=6"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/imurmurhash": {



      "version": "0.1.4",



      "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz",



      "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.8.19"



      }



    },



    "node_modules/inflight": {



      "version": "1.0.6",



      "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz",



      "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==",



      "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "once": "^1.3.0",



        "wrappy": "1"



      }



    },



    "node_modules/inherits": {



      "version": "2.0.4",



      "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz",



      "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/inline-style-parser": {



      "version": "0.2.4",



      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz",



      "integrity": "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==",



      "license": "MIT"



    },



    "node_modules/is-alphabetical": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz",



      "integrity": "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/is-alphanumerical": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz",



      "integrity": "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",



      "license": "MIT",



      "dependencies": {



        "is-alphabetical": "^2.0.0",



        "is-decimal": "^2.0.0"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/is-binary-path": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",



      "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "binary-extensions": "^2.0.0"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/is-core-module": {



      "version": "2.16.1",



      "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz",



      "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "hasown": "^2.0.2"



      },



      "engines": {



        "node": ">= 0.4"



      },



      "funding": {



        "url": "https://github.com/sponsors/ljharb"



      }



    },



    "node_modules/is-decimal": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz",



      "integrity": "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/is-extglob": {



      "version": "2.1.1",



      "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",



      "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/is-fullwidth-code-point": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",



      "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/is-glob": {



      "version": "4.0.3",



      "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",



      "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "is-extglob": "^2.1.1"



      },



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/is-hexadecimal": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz",



      "integrity": "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/is-number": {



      "version": "7.0.0",



      "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",



      "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.12.0"



      }



    },



    "node_modules/is-path-inside": {



      "version": "3.0.3",



      "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz",



      "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/is-plain-obj": {



      "version": "4.1.0",



      "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",



      "integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==",



      "license": "MIT",



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/isexe": {



      "version": "2.0.0",



      "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",



      "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/jackspeak": {



      "version": "3.4.3",



      "resolved": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz",



      "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==",



      "dev": true,



      "license": "BlueOak-1.0.0",



      "dependencies": {



        "@isaacs/cliui": "^8.0.2"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      },



      "optionalDependencies": {



        "@pkgjs/parseargs": "^0.11.0"



      }



    },



    "node_modules/jiti": {



      "version": "1.21.7",



      "resolved": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz",



      "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "jiti": "bin/jiti.js"



      }



    },



    "node_modules/js-tokens": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",



      "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==",



      "license": "MIT"



    },



    "node_modules/js-yaml": {



      "version": "4.1.0",



      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz",



      "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "argparse": "^2.0.1"



      },



      "bin": {



        "js-yaml": "bin/js-yaml.js"



      }



    },



    "node_modules/jsesc": {



      "version": "3.1.0",



      "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz",



      "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "jsesc": "bin/jsesc"



      },



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/json-buffer": {



      "version": "3.0.1",



      "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz",



      "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/json-schema-traverse": {



      "version": "0.4.1",



      "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz",



      "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/json-stable-stringify-without-jsonify": {



      "version": "1.0.1",



      "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz",



      "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/json5": {



      "version": "2.2.3",



      "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz",



      "integrity": "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "json5": "lib/cli.js"



      },



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/keyv": {



      "version": "4.5.4",



      "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz",



      "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "json-buffer": "3.0.1"



      }



    },



    "node_modules/levn": {



      "version": "0.4.1",



      "resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz",



      "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "prelude-ls": "^1.2.1",



        "type-check": "~0.4.0"



      },



      "engines": {



        "node": ">= 0.8.0"



      }



    },



    "node_modules/lilconfig": {



      "version": "3.1.3",



      "resolved": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz",



      "integrity": "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=14"



      },



      "funding": {



        "url": "https://github.com/sponsors/antonk52"



      }



    },



    "node_modules/lines-and-columns": {



      "version": "1.2.4",



      "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",



      "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/locate-path": {



      "version": "6.0.0",



      "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz",



      "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "p-locate": "^5.0.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/lodash.merge": {



      "version": "4.6.2",



      "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz",



      "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/longest-streak": {



      "version": "3.1.0",



      "resolved": "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz",



      "integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/loose-envify": {



      "version": "1.4.0",



      "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",



      "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",



      "license": "MIT",



      "dependencies": {



        "js-tokens": "^3.0.0 || ^4.0.0"



      },



      "bin": {



        "loose-envify": "cli.js"



      }



    },



    "node_modules/lru-cache": {



      "version": "5.1.1",



      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz",



      "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "yallist": "^3.0.2"



      }



    },



    "node_modules/lucide-react": {



      "version": "0.300.0",



      "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.300.0.tgz",



      "integrity": "sha512-rQxUUCmWAvNLoAsMZ5j04b2+OJv6UuNLYMY7VK0eVlm4aTwUEjEEHc09/DipkNIlhXUSDn2xoyIzVT0uh7dRsg==",



      "license": "ISC",



      "peerDependencies": {



        "react": "^16.5.1 || ^17.0.0 || ^18.0.0"



      }



    },



    "node_modules/mdast-util-from-markdown": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz",



      "integrity": "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==",



      "license": "MIT",



      "dependencies": {



        "@types/mdast": "^4.0.0",



        "@types/unist": "^3.0.0",



        "decode-named-character-reference": "^1.0.0",



        "devlop": "^1.0.0",



        "mdast-util-to-string": "^4.0.0",



        "micromark": "^4.0.0",



        "micromark-util-decode-numeric-character-reference": "^2.0.0",



        "micromark-util-decode-string": "^2.0.0",



        "micromark-util-normalize-identifier": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0",



        "unist-util-stringify-position": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-mdx-expression": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz",



      "integrity": "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==",



      "license": "MIT",



      "dependencies": {



        "@types/estree-jsx": "^1.0.0",



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "devlop": "^1.0.0",



        "mdast-util-from-markdown": "^2.0.0",



        "mdast-util-to-markdown": "^2.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-mdx-jsx": {



      "version": "3.2.0",



      "resolved": "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz",



      "integrity": "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==",



      "license": "MIT",



      "dependencies": {



        "@types/estree-jsx": "^1.0.0",



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "@types/unist": "^3.0.0",



        "ccount": "^2.0.0",



        "devlop": "^1.1.0",



        "mdast-util-from-markdown": "^2.0.0",



        "mdast-util-to-markdown": "^2.0.0",



        "parse-entities": "^4.0.0",



        "stringify-entities": "^4.0.0",



        "unist-util-stringify-position": "^4.0.0",



        "vfile-message": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-mdxjs-esm": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz",



      "integrity": "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",



      "license": "MIT",



      "dependencies": {



        "@types/estree-jsx": "^1.0.0",



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "devlop": "^1.0.0",



        "mdast-util-from-markdown": "^2.0.0",



        "mdast-util-to-markdown": "^2.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-phrasing": {



      "version": "4.1.0",



      "resolved": "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz",



      "integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",



      "license": "MIT",



      "dependencies": {



        "@types/mdast": "^4.0.0",



        "unist-util-is": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-to-hast": {



      "version": "13.2.0",



      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz",



      "integrity": "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==",



      "license": "MIT",



      "dependencies": {



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "@ungap/structured-clone": "^1.0.0",



        "devlop": "^1.0.0",



        "micromark-util-sanitize-uri": "^2.0.0",



        "trim-lines": "^3.0.0",



        "unist-util-position": "^5.0.0",



        "unist-util-visit": "^5.0.0",



        "vfile": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-to-markdown": {



      "version": "2.1.2",



      "resolved": "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz",



      "integrity": "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==",



      "license": "MIT",



      "dependencies": {



        "@types/mdast": "^4.0.0",



        "@types/unist": "^3.0.0",



        "longest-streak": "^3.0.0",



        "mdast-util-phrasing": "^4.0.0",



        "mdast-util-to-string": "^4.0.0",



        "micromark-util-classify-character": "^2.0.0",



        "micromark-util-decode-string": "^2.0.0",



        "unist-util-visit": "^5.0.0",



        "zwitch": "^2.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/mdast-util-to-string": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz",



      "integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",



      "license": "MIT",



      "dependencies": {



        "@types/mdast": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/merge2": {



      "version": "1.4.1",



      "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",



      "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/micromark": {



      "version": "4.0.2",



      "resolved": "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz",



      "integrity": "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "@types/debug": "^4.0.0",



        "debug": "^4.0.0",



        "decode-named-character-reference": "^1.0.0",



        "devlop": "^1.0.0",



        "micromark-core-commonmark": "^2.0.0",



        "micromark-factory-space": "^2.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-chunked": "^2.0.0",



        "micromark-util-combine-extensions": "^2.0.0",



        "micromark-util-decode-numeric-character-reference": "^2.0.0",



        "micromark-util-encode": "^2.0.0",



        "micromark-util-normalize-identifier": "^2.0.0",



        "micromark-util-resolve-all": "^2.0.0",



        "micromark-util-sanitize-uri": "^2.0.0",



        "micromark-util-subtokenize": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-core-commonmark": {



      "version": "2.0.3",



      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz",



      "integrity": "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "decode-named-character-reference": "^1.0.0",



        "devlop": "^1.0.0",



        "micromark-factory-destination": "^2.0.0",



        "micromark-factory-label": "^2.0.0",



        "micromark-factory-space": "^2.0.0",



        "micromark-factory-title": "^2.0.0",



        "micromark-factory-whitespace": "^2.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-chunked": "^2.0.0",



        "micromark-util-classify-character": "^2.0.0",



        "micromark-util-html-tag-name": "^2.0.0",



        "micromark-util-normalize-identifier": "^2.0.0",



        "micromark-util-resolve-all": "^2.0.0",



        "micromark-util-subtokenize": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-factory-destination": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz",



      "integrity": "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-character": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-factory-label": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz",



      "integrity": "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "devlop": "^1.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-factory-space": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz",



      "integrity": "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-character": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-factory-title": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz",



      "integrity": "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-factory-space": "^2.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-factory-whitespace": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz",



      "integrity": "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-factory-space": "^2.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-character": {



      "version": "2.1.1",



      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz",



      "integrity": "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-chunked": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz",



      "integrity": "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-symbol": "^2.0.0"



      }



    },



    "node_modules/micromark-util-classify-character": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz",



      "integrity": "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-character": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-combine-extensions": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz",



      "integrity": "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-chunked": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-decode-numeric-character-reference": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz",



      "integrity": "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-symbol": "^2.0.0"



      }



    },



    "node_modules/micromark-util-decode-string": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz",



      "integrity": "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "decode-named-character-reference": "^1.0.0",



        "micromark-util-character": "^2.0.0",



        "micromark-util-decode-numeric-character-reference": "^2.0.0",



        "micromark-util-symbol": "^2.0.0"



      }



    },



    "node_modules/micromark-util-encode": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz",



      "integrity": "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT"



    },



    "node_modules/micromark-util-html-tag-name": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz",



      "integrity": "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT"



    },



    "node_modules/micromark-util-normalize-identifier": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz",



      "integrity": "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-symbol": "^2.0.0"



      }



    },



    "node_modules/micromark-util-resolve-all": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz",



      "integrity": "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-sanitize-uri": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz",



      "integrity": "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "micromark-util-character": "^2.0.0",



        "micromark-util-encode": "^2.0.0",



        "micromark-util-symbol": "^2.0.0"



      }



    },



    "node_modules/micromark-util-subtokenize": {



      "version": "2.1.0",



      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz",



      "integrity": "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "devlop": "^1.0.0",



        "micromark-util-chunked": "^2.0.0",



        "micromark-util-symbol": "^2.0.0",



        "micromark-util-types": "^2.0.0"



      }



    },



    "node_modules/micromark-util-symbol": {



      "version": "2.0.1",



      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz",



      "integrity": "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT"



    },



    "node_modules/micromark-util-types": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz",



      "integrity": "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==",



      "funding": [



        {



          "type": "GitHub Sponsors",



          "url": "https://github.com/sponsors/unifiedjs"



        },



        {



          "type": "OpenCollective",



          "url": "https://opencollective.com/unified"



        }



      ],



      "license": "MIT"



    },



    "node_modules/micromatch": {



      "version": "4.0.8",



      "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz",



      "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "braces": "^3.0.3",



        "picomatch": "^2.3.1"



      },



      "engines": {



        "node": ">=8.6"



      }



    },



    "node_modules/minimatch": {



      "version": "9.0.3",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz",



      "integrity": "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^2.0.1"



      },



      "engines": {



        "node": ">=16 || 14 >=14.17"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/minipass": {



      "version": "7.1.2",



      "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz",



      "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==",



      "dev": true,



      "license": "ISC",



      "engines": {



        "node": ">=16 || 14 >=14.17"



      }



    },



    "node_modules/ms": {



      "version": "2.1.3",



      "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",



      "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",



      "license": "MIT"



    },



    "node_modules/mz": {



      "version": "2.7.0",



      "resolved": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz",



      "integrity": "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "any-promise": "^1.0.0",



        "object-assign": "^4.0.1",



        "thenify-all": "^1.0.0"



      }



    },



    "node_modules/nanoid": {



      "version": "3.3.11",



      "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz",



      "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==",



      "dev": true,



      "funding": [



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "bin": {



        "nanoid": "bin/nanoid.cjs"



      },



      "engines": {



        "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"



      }



    },



    "node_modules/natural-compare": {



      "version": "1.4.0",



      "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz",



      "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/node-releases": {



      "version": "2.0.19",



      "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz",



      "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/normalize-path": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",



      "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/normalize-range": {



      "version": "0.1.2",



      "resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz",



      "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/object-assign": {



      "version": "4.1.1",



      "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",



      "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/object-hash": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz",



      "integrity": "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/once": {



      "version": "1.4.0",



      "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz",



      "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "wrappy": "1"



      }



    },



    "node_modules/optionator": {



      "version": "0.9.4",



      "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz",



      "integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "deep-is": "^0.1.3",



        "fast-levenshtein": "^2.0.6",



        "levn": "^0.4.1",



        "prelude-ls": "^1.2.1",



        "type-check": "^0.4.0",



        "word-wrap": "^1.2.5"



      },



      "engines": {



        "node": ">= 0.8.0"



      }



    },



    "node_modules/p-limit": {



      "version": "3.1.0",



      "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz",



      "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "yocto-queue": "^0.1.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/p-locate": {



      "version": "5.0.0",



      "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz",



      "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "p-limit": "^3.0.2"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/package-json-from-dist": {



      "version": "1.0.1",



      "resolved": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz",



      "integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==",



      "dev": true,



      "license": "BlueOak-1.0.0"



    },



    "node_modules/parent-module": {



      "version": "1.0.1",



      "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz",



      "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "callsites": "^3.0.0"



      },



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/parse-entities": {



      "version": "4.0.2",



      "resolved": "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz",



      "integrity": "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^2.0.0",



        "character-entities-legacy": "^3.0.0",



        "character-reference-invalid": "^2.0.0",



        "decode-named-character-reference": "^1.0.0",



        "is-alphanumerical": "^2.0.0",



        "is-decimal": "^2.0.0",



        "is-hexadecimal": "^2.0.0"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/parse-entities/node_modules/@types/unist": {



      "version": "2.0.11",



      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",



      "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==",



      "license": "MIT"



    },



    "node_modules/path-exists": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz",



      "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/path-is-absolute": {



      "version": "1.0.1",



      "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz",



      "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/path-key": {



      "version": "3.1.1",



      "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",



      "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/path-parse": {



      "version": "1.0.7",



      "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",



      "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/path-scurry": {



      "version": "1.11.1",



      "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz",



      "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==",



      "dev": true,



      "license": "BlueOak-1.0.0",



      "dependencies": {



        "lru-cache": "^10.2.0",



        "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"



      },



      "engines": {



        "node": ">=16 || 14 >=14.18"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/path-scurry/node_modules/lru-cache": {



      "version": "10.4.3",



      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz",



      "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/path-type": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz",



      "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/picocolors": {



      "version": "1.1.1",



      "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz",



      "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/picomatch": {



      "version": "2.3.1",



      "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",



      "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8.6"



      },



      "funding": {



        "url": "https://github.com/sponsors/jonschlinkert"



      }



    },



    "node_modules/pify": {



      "version": "2.3.0",



      "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz",



      "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/pirates": {



      "version": "4.0.7",



      "resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz",



      "integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 6"



      }



    },



    "node_modules/postcss": {



      "version": "8.5.6",



      "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz",



      "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/postcss/"



        },



        {



          "type": "tidelift",



          "url": "https://tidelift.com/funding/github/npm/postcss"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "nanoid": "^3.3.11",



        "picocolors": "^1.1.1",



        "source-map-js": "^1.2.1"



      },



      "engines": {



        "node": "^10 || ^12 || >=14"



      }



    },



    "node_modules/postcss-import": {



      "version": "15.1.0",



      "resolved": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz",



      "integrity": "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "postcss-value-parser": "^4.0.0",



        "read-cache": "^1.0.0",



        "resolve": "^1.1.7"



      },



      "engines": {



        "node": ">=14.0.0"



      },



      "peerDependencies": {



        "postcss": "^8.0.0"



      }



    },



    "node_modules/postcss-js": {



      "version": "4.0.1",



      "resolved": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz",



      "integrity": "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "camelcase-css": "^2.0.1"



      },



      "engines": {



        "node": "^12 || ^14 || >= 16"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/postcss/"



      },



      "peerDependencies": {



        "postcss": "^8.4.21"



      }



    },



    "node_modules/postcss-load-config": {



      "version": "4.0.2",



      "resolved": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz",



      "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/postcss/"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "lilconfig": "^3.0.0",



        "yaml": "^2.3.4"



      },



      "engines": {



        "node": ">= 14"



      },



      "peerDependencies": {



        "postcss": ">=8.0.9",



        "ts-node": ">=9.0.0"



      },



      "peerDependenciesMeta": {



        "postcss": {



          "optional": true



        },



        "ts-node": {



          "optional": true



        }



      }



    },



    "node_modules/postcss-nested": {



      "version": "6.2.0",



      "resolved": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz",



      "integrity": "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/postcss/"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "postcss-selector-parser": "^6.1.1"



      },



      "engines": {



        "node": ">=12.0"



      },



      "peerDependencies": {



        "postcss": "^8.2.14"



      }



    },



    "node_modules/postcss-selector-parser": {



      "version": "6.1.2",



      "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz",



      "integrity": "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "cssesc": "^3.0.0",



        "util-deprecate": "^1.0.2"



      },



      "engines": {



        "node": ">=4"



      }



    },



    "node_modules/postcss-value-parser": {



      "version": "4.2.0",



      "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz",



      "integrity": "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/prelude-ls": {



      "version": "1.2.1",



      "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz",



      "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 0.8.0"



      }



    },



    "node_modules/prettier": {



      "version": "3.6.2",



      "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz",



      "integrity": "sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==",



      "dev": true,



      "license": "MIT",



      "bin": {



        "prettier": "bin/prettier.cjs"



      },



      "engines": {



        "node": ">=14"



      },



      "funding": {



        "url": "https://github.com/prettier/prettier?sponsor=1"



      }



    },



    "node_modules/property-information": {



      "version": "7.1.0",



      "resolved": "https://registry.npmjs.org/property-information/-/property-information-7.1.0.tgz",



      "integrity": "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/punycode": {



      "version": "2.3.1",



      "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz",



      "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=6"



      }



    },



    "node_modules/queue-microtask": {



      "version": "1.2.3",



      "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz",



      "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",



      "dev": true,



      "funding": [



        {



          "type": "github",



          "url": "https://github.com/sponsors/feross"



        },



        {



          "type": "patreon",



          "url": "https://www.patreon.com/feross"



        },



        {



          "type": "consulting",



          "url": "https://feross.org/support"



        }



      ],



      "license": "MIT"



    },



    "node_modules/react": {



      "version": "18.3.1",



      "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz",



      "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==",



      "license": "MIT",



      "dependencies": {



        "loose-envify": "^1.1.0"



      },



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/react-dom": {



      "version": "18.3.1",



      "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz",



      "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==",



      "license": "MIT",



      "dependencies": {



        "loose-envify": "^1.1.0",



        "scheduler": "^0.23.2"



      },



      "peerDependencies": {



        "react": "^18.3.1"



      }



    },



    "node_modules/react-markdown": {



      "version": "9.1.0",



      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-9.1.0.tgz",



      "integrity": "sha512-xaijuJB0kzGiUdG7nc2MOMDUDBWPyGAjZtUrow9XxUeua8IqeP+VlIfAZ3bphpcLTnSZXz6z9jcVC/TCwbfgdw==",



      "license": "MIT",



      "dependencies": {



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "devlop": "^1.0.0",



        "hast-util-to-jsx-runtime": "^2.0.0",



        "html-url-attributes": "^3.0.0",



        "mdast-util-to-hast": "^13.0.0",



        "remark-parse": "^11.0.0",



        "remark-rehype": "^11.0.0",



        "unified": "^11.0.0",



        "unist-util-visit": "^5.0.0",



        "vfile": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      },



      "peerDependencies": {



        "@types/react": ">=18",



        "react": ">=18"



      }



    },



    "node_modules/react-refresh": {



      "version": "0.17.0",



      "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz",



      "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/read-cache": {



      "version": "1.0.0",



      "resolved": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz",



      "integrity": "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "pify": "^2.3.0"



      }



    },



    "node_modules/readdirp": {



      "version": "3.6.0",



      "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz",



      "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "picomatch": "^2.2.1"



      },



      "engines": {



        "node": ">=8.10.0"



      }



    },



    "node_modules/remark-parse": {



      "version": "11.0.0",



      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz",



      "integrity": "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==",



      "license": "MIT",



      "dependencies": {



        "@types/mdast": "^4.0.0",



        "mdast-util-from-markdown": "^2.0.0",



        "micromark-util-types": "^2.0.0",



        "unified": "^11.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/remark-rehype": {



      "version": "11.1.2",



      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz",



      "integrity": "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==",



      "license": "MIT",



      "dependencies": {



        "@types/hast": "^3.0.0",



        "@types/mdast": "^4.0.0",



        "mdast-util-to-hast": "^13.0.0",



        "unified": "^11.0.0",



        "vfile": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/resolve": {



      "version": "1.22.10",



      "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz",



      "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "is-core-module": "^2.16.0",



        "path-parse": "^1.0.7",



        "supports-preserve-symlinks-flag": "^1.0.0"



      },



      "bin": {



        "resolve": "bin/resolve"



      },



      "engines": {



        "node": ">= 0.4"



      },



      "funding": {



        "url": "https://github.com/sponsors/ljharb"



      }



    },



    "node_modules/resolve-from": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz",



      "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=4"



      }



    },



    "node_modules/reusify": {



      "version": "1.1.0",



      "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz",



      "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "iojs": ">=1.0.0",



        "node": ">=0.10.0"



      }



    },



    "node_modules/rimraf": {



      "version": "3.0.2",



      "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz",



      "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==",



      "deprecated": "Rimraf versions prior to v4 are no longer supported",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "glob": "^7.1.3"



      },



      "bin": {



        "rimraf": "bin.js"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/rollup": {



      "version": "4.45.1",



      "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.45.1.tgz",



      "integrity": "sha512-4iya7Jb76fVpQyLoiVpzUrsjQ12r3dM7fIVz+4NwoYvZOShknRmiv+iu9CClZml5ZLGb0XMcYLutK6w9tgxHDw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@types/estree": "1.0.8"



      },



      "bin": {



        "rollup": "dist/bin/rollup"



      },



      "engines": {



        "node": ">=18.0.0",



        "npm": ">=8.0.0"



      },



      "optionalDependencies": {



        "@rollup/rollup-android-arm-eabi": "4.45.1",



        "@rollup/rollup-android-arm64": "4.45.1",



        "@rollup/rollup-darwin-arm64": "4.45.1",



        "@rollup/rollup-darwin-x64": "4.45.1",



        "@rollup/rollup-freebsd-arm64": "4.45.1",



        "@rollup/rollup-freebsd-x64": "4.45.1",



        "@rollup/rollup-linux-arm-gnueabihf": "4.45.1",



        "@rollup/rollup-linux-arm-musleabihf": "4.45.1",



        "@rollup/rollup-linux-arm64-gnu": "4.45.1",



        "@rollup/rollup-linux-arm64-musl": "4.45.1",



        "@rollup/rollup-linux-loongarch64-gnu": "4.45.1",



        "@rollup/rollup-linux-powerpc64le-gnu": "4.45.1",



        "@rollup/rollup-linux-riscv64-gnu": "4.45.1",



        "@rollup/rollup-linux-riscv64-musl": "4.45.1",



        "@rollup/rollup-linux-s390x-gnu": "4.45.1",



        "@rollup/rollup-linux-x64-gnu": "4.45.1",



        "@rollup/rollup-linux-x64-musl": "4.45.1",



        "@rollup/rollup-win32-arm64-msvc": "4.45.1",



        "@rollup/rollup-win32-ia32-msvc": "4.45.1",



        "@rollup/rollup-win32-x64-msvc": "4.45.1",



        "fsevents": "~2.3.2"



      }



    },



    "node_modules/run-parallel": {



      "version": "1.2.0",



      "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz",



      "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",



      "dev": true,



      "funding": [



        {



          "type": "github",



          "url": "https://github.com/sponsors/feross"



        },



        {



          "type": "patreon",



          "url": "https://www.patreon.com/feross"



        },



        {



          "type": "consulting",



          "url": "https://feross.org/support"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "queue-microtask": "^1.2.2"



      }



    },



    "node_modules/scheduler": {



      "version": "0.23.2",



      "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz",



      "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==",



      "license": "MIT",



      "dependencies": {



        "loose-envify": "^1.1.0"



      }



    },



    "node_modules/semver": {



      "version": "7.7.2",



      "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz",



      "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==",



      "dev": true,



      "license": "ISC",



      "bin": {



        "semver": "bin/semver.js"



      },



      "engines": {



        "node": ">=10"



      }



    },



    "node_modules/shebang-command": {



      "version": "2.0.0",



      "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",



      "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "shebang-regex": "^3.0.0"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/shebang-regex": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz",



      "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/signal-exit": {



      "version": "4.1.0",



      "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz",



      "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",



      "dev": true,



      "license": "ISC",



      "engines": {



        "node": ">=14"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/slash": {



      "version": "3.0.0",



      "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz",



      "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/source-map-js": {



      "version": "1.2.1",



      "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz",



      "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==",



      "dev": true,



      "license": "BSD-3-Clause",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/space-separated-tokens": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz",



      "integrity": "sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/string-width": {



      "version": "5.1.2",



      "resolved": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz",



      "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "eastasianwidth": "^0.2.0",



        "emoji-regex": "^9.2.2",



        "strip-ansi": "^7.0.1"



      },



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/string-width-cjs": {



      "name": "string-width",



      "version": "4.2.3",



      "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",



      "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "emoji-regex": "^8.0.0",



        "is-fullwidth-code-point": "^3.0.0",



        "strip-ansi": "^6.0.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/string-width-cjs/node_modules/emoji-regex": {



      "version": "8.0.0",



      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",



      "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/string-width/node_modules/ansi-regex": {



      "version": "6.1.0",



      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",



      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/ansi-regex?sponsor=1"



      }



    },



    "node_modules/string-width/node_modules/strip-ansi": {



      "version": "7.1.0",



      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",



      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-regex": "^6.0.1"



      },



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/strip-ansi?sponsor=1"



      }



    },



    "node_modules/stringify-entities": {



      "version": "4.0.4",



      "resolved": "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz",



      "integrity": "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==",



      "license": "MIT",



      "dependencies": {



        "character-entities-html4": "^2.0.0",



        "character-entities-legacy": "^3.0.0"



      },



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/strip-ansi": {



      "version": "6.0.1",



      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",



      "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-regex": "^5.0.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/strip-ansi-cjs": {



      "name": "strip-ansi",



      "version": "6.0.1",



      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",



      "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-regex": "^5.0.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/strip-json-comments": {



      "version": "3.1.1",



      "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz",



      "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=8"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/style-to-js": {



      "version": "1.1.17",



      "resolved": "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.17.tgz",



      "integrity": "sha512-xQcBGDxJb6jjFCTzvQtfiPn6YvvP2O8U1MDIPNfJQlWMYfktPy+iGsHE7cssjs7y84d9fQaK4UF3RIJaAHSoYA==",



      "license": "MIT",



      "dependencies": {



        "style-to-object": "1.0.9"



      }



    },



    "node_modules/style-to-object": {



      "version": "1.0.9",



      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.9.tgz",



      "integrity": "sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==",



      "license": "MIT",



      "dependencies": {



        "inline-style-parser": "0.2.4"



      }



    },



    "node_modules/sucrase": {



      "version": "3.35.0",



      "resolved": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz",



      "integrity": "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@jridgewell/gen-mapping": "^0.3.2",



        "commander": "^4.0.0",



        "glob": "^10.3.10",



        "lines-and-columns": "^1.1.6",



        "mz": "^2.7.0",



        "pirates": "^4.0.1",



        "ts-interface-checker": "^0.1.9"



      },



      "bin": {



        "sucrase": "bin/sucrase",



        "sucrase-node": "bin/sucrase-node"



      },



      "engines": {



        "node": ">=16 || 14 >=14.17"



      }



    },



    "node_modules/sucrase/node_modules/glob": {



      "version": "10.4.5",



      "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz",



      "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "foreground-child": "^3.1.0",



        "jackspeak": "^3.1.2",



        "minimatch": "^9.0.4",



        "minipass": "^7.1.2",



        "package-json-from-dist": "^1.0.0",



        "path-scurry": "^1.11.1"



      },



      "bin": {



        "glob": "dist/esm/bin.mjs"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/sucrase/node_modules/minimatch": {



      "version": "9.0.5",



      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz",



      "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "brace-expansion": "^2.0.1"



      },



      "engines": {



        "node": ">=16 || 14 >=14.17"



      },



      "funding": {



        "url": "https://github.com/sponsors/isaacs"



      }



    },



    "node_modules/supports-color": {



      "version": "7.2.0",



      "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz",



      "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "has-flag": "^4.0.0"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/supports-preserve-symlinks-flag": {



      "version": "1.0.0",



      "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz",



      "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">= 0.4"



      },



      "funding": {



        "url": "https://github.com/sponsors/ljharb"



      }



    },



    "node_modules/tailwind-merge": {



      "version": "2.6.0",



      "resolved": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz",



      "integrity": "sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/dcastil"



      }



    },



    "node_modules/tailwindcss": {



      "version": "3.4.17",



      "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz",



      "integrity": "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "@alloc/quick-lru": "^5.2.0",



        "arg": "^5.0.2",



        "chokidar": "^3.6.0",



        "didyoumean": "^1.2.2",



        "dlv": "^1.1.3",



        "fast-glob": "^3.3.2",



        "glob-parent": "^6.0.2",



        "is-glob": "^4.0.3",



        "jiti": "^1.21.6",



        "lilconfig": "^3.1.3",



        "micromatch": "^4.0.8",



        "normalize-path": "^3.0.0",



        "object-hash": "^3.0.0",



        "picocolors": "^1.1.1",



        "postcss": "^8.4.47",



        "postcss-import": "^15.1.0",



        "postcss-js": "^4.0.1",



        "postcss-load-config": "^4.0.2",



        "postcss-nested": "^6.2.0",



        "postcss-selector-parser": "^6.1.2",



        "resolve": "^1.22.8",



        "sucrase": "^3.35.0"



      },



      "bin": {



        "tailwind": "lib/cli.js",



        "tailwindcss": "lib/cli.js"



      },



      "engines": {



        "node": ">=14.0.0"



      }



    },



    "node_modules/text-table": {



      "version": "0.2.0",



      "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz",



      "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/thenify": {



      "version": "3.3.1",



      "resolved": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz",



      "integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "any-promise": "^1.0.0"



      }



    },



    "node_modules/thenify-all": {



      "version": "1.6.0",



      "resolved": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz",



      "integrity": "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "thenify": ">= 3.1.0 < 4"



      },



      "engines": {



        "node": ">=0.8"



      }



    },



    "node_modules/to-regex-range": {



      "version": "5.0.1",



      "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",



      "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "is-number": "^7.0.0"



      },



      "engines": {



        "node": ">=8.0"



      }



    },



    "node_modules/trim-lines": {



      "version": "3.0.1",



      "resolved": "https://registry.npmjs.org/trim-lines/-/trim-lines-3.0.1.tgz",



      "integrity": "sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/trough": {



      "version": "2.2.0",



      "resolved": "https://registry.npmjs.org/trough/-/trough-2.2.0.tgz",



      "integrity": "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    },



    "node_modules/ts-api-utils": {



      "version": "1.4.3",



      "resolved": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz",



      "integrity": "sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=16"



      },



      "peerDependencies": {



        "typescript": ">=4.2.0"



      }



    },



    "node_modules/ts-interface-checker": {



      "version": "0.1.13",



      "resolved": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz",



      "integrity": "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==",



      "dev": true,



      "license": "Apache-2.0"



    },



    "node_modules/type-check": {



      "version": "0.4.0",



      "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz",



      "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "prelude-ls": "^1.2.1"



      },



      "engines": {



        "node": ">= 0.8.0"



      }



    },



    "node_modules/type-fest": {



      "version": "0.20.2",



      "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz",



      "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==",



      "dev": true,



      "license": "(MIT OR CC0-1.0)",



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/typescript": {



      "version": "5.8.3",



      "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz",



      "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==",



      "dev": true,



      "license": "Apache-2.0",



      "bin": {



        "tsc": "bin/tsc",



        "tsserver": "bin/tsserver"



      },



      "engines": {



        "node": ">=14.17"



      }



    },



    "node_modules/unified": {



      "version": "11.0.5",



      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz",



      "integrity": "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0",



        "bail": "^2.0.0",



        "devlop": "^1.0.0",



        "extend": "^3.0.0",



        "is-plain-obj": "^4.0.0",



        "trough": "^2.0.0",



        "vfile": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/unist-util-is": {



      "version": "6.0.0",



      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",



      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/unist-util-position": {



      "version": "5.0.0",



      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz",



      "integrity": "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/unist-util-stringify-position": {



      "version": "4.0.0",



      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",



      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/unist-util-visit": {



      "version": "5.0.0",



      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",



      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0",



        "unist-util-is": "^6.0.0",



        "unist-util-visit-parents": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/unist-util-visit-parents": {



      "version": "6.0.1",



      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",



      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0",



        "unist-util-is": "^6.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/update-browserslist-db": {



      "version": "1.1.3",



      "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz",



      "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==",



      "dev": true,



      "funding": [



        {



          "type": "opencollective",



          "url": "https://opencollective.com/browserslist"



        },



        {



          "type": "tidelift",



          "url": "https://tidelift.com/funding/github/npm/browserslist"



        },



        {



          "type": "github",



          "url": "https://github.com/sponsors/ai"



        }



      ],



      "license": "MIT",



      "dependencies": {



        "escalade": "^3.2.0",



        "picocolors": "^1.1.1"



      },



      "bin": {



        "update-browserslist-db": "cli.js"



      },



      "peerDependencies": {



        "browserslist": ">= 4.21.0"



      }



    },



    "node_modules/uri-js": {



      "version": "4.4.1",



      "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz",



      "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==",



      "dev": true,



      "license": "BSD-2-Clause",



      "dependencies": {



        "punycode": "^2.1.0"



      }



    },



    "node_modules/use-sync-external-store": {



      "version": "1.5.0",



      "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz",



      "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==",



      "license": "MIT",



      "peerDependencies": {



        "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"



      }



    },



    "node_modules/util-deprecate": {



      "version": "1.0.2",



      "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz",



      "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/vfile": {



      "version": "6.0.3",



      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz",



      "integrity": "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0",



        "vfile-message": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/vfile-message": {



      "version": "4.0.2",



      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",



      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",



      "license": "MIT",



      "dependencies": {



        "@types/unist": "^3.0.0",



        "unist-util-stringify-position": "^4.0.0"



      },



      "funding": {



        "type": "opencollective",



        "url": "https://opencollective.com/unified"



      }



    },



    "node_modules/vite": {



      "version": "5.4.19",



      "resolved": "https://registry.npmjs.org/vite/-/vite-5.4.19.tgz",



      "integrity": "sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "esbuild": "^0.21.3",



        "postcss": "^8.4.43",



        "rollup": "^4.20.0"



      },



      "bin": {



        "vite": "bin/vite.js"



      },



      "engines": {



        "node": "^18.0.0 || >=20.0.0"



      },



      "funding": {



        "url": "https://github.com/vitejs/vite?sponsor=1"



      },



      "optionalDependencies": {



        "fsevents": "~2.3.3"



      },



      "peerDependencies": {



        "@types/node": "^18.0.0 || >=20.0.0",



        "less": "*",



        "lightningcss": "^1.21.0",



        "sass": "*",



        "sass-embedded": "*",



        "stylus": "*",



        "sugarss": "*",



        "terser": "^5.4.0"



      },



      "peerDependenciesMeta": {



        "@types/node": {



          "optional": true



        },



        "less": {



          "optional": true



        },



        "lightningcss": {



          "optional": true



        },



        "sass": {



          "optional": true



        },



        "sass-embedded": {



          "optional": true



        },



        "stylus": {



          "optional": true



        },



        "sugarss": {



          "optional": true



        },



        "terser": {



          "optional": true



        }



      }



    },



    "node_modules/which": {



      "version": "2.0.2",



      "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz",



      "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",



      "dev": true,



      "license": "ISC",



      "dependencies": {



        "isexe": "^2.0.0"



      },



      "bin": {



        "node-which": "bin/node-which"



      },



      "engines": {



        "node": ">= 8"



      }



    },



    "node_modules/word-wrap": {



      "version": "1.2.5",



      "resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz",



      "integrity": "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=0.10.0"



      }



    },



    "node_modules/wrap-ansi": {



      "version": "8.1.0",



      "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz",



      "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-styles": "^6.1.0",



        "string-width": "^5.0.1",



        "strip-ansi": "^7.0.1"



      },



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/wrap-ansi?sponsor=1"



      }



    },



    "node_modules/wrap-ansi-cjs": {



      "name": "wrap-ansi",



      "version": "7.0.0",



      "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz",



      "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-styles": "^4.0.0",



        "string-width": "^4.1.0",



        "strip-ansi": "^6.0.0"



      },



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/chalk/wrap-ansi?sponsor=1"



      }



    },



    "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {



      "version": "8.0.0",



      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",



      "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",



      "dev": true,



      "license": "MIT"



    },



    "node_modules/wrap-ansi-cjs/node_modules/string-width": {



      "version": "4.2.3",



      "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",



      "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "emoji-regex": "^8.0.0",



        "is-fullwidth-code-point": "^3.0.0",



        "strip-ansi": "^6.0.1"



      },



      "engines": {



        "node": ">=8"



      }



    },



    "node_modules/wrap-ansi/node_modules/ansi-regex": {



      "version": "6.1.0",



      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",



      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/ansi-regex?sponsor=1"



      }



    },



    "node_modules/wrap-ansi/node_modules/ansi-styles": {



      "version": "6.2.1",



      "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",



      "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/ansi-styles?sponsor=1"



      }



    },



    "node_modules/wrap-ansi/node_modules/strip-ansi": {



      "version": "7.1.0",



      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",



      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",



      "dev": true,



      "license": "MIT",



      "dependencies": {



        "ansi-regex": "^6.0.1"



      },



      "engines": {



        "node": ">=12"



      },



      "funding": {



        "url": "https://github.com/chalk/strip-ansi?sponsor=1"



      }



    },



    "node_modules/wrappy": {



      "version": "1.0.2",



      "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz",



      "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/yallist": {



      "version": "3.1.1",



      "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz",



      "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==",



      "dev": true,



      "license": "ISC"



    },



    "node_modules/yaml": {



      "version": "2.8.0",



      "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz",



      "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==",



      "dev": true,



      "license": "ISC",



      "bin": {



        "yaml": "bin.mjs"



      },



      "engines": {



        "node": ">= 14.6"



      }



    },



    "node_modules/yocto-queue": {



      "version": "0.1.0",



      "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",



      "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",



      "dev": true,



      "license": "MIT",



      "engines": {



        "node": ">=10"



      },



      "funding": {



        "url": "https://github.com/sponsors/sindresorhus"



      }



    },



    "node_modules/zustand": {



      "version": "4.5.7",



      "resolved": "https://registry.npmjs.org/zustand/-/zustand-4.5.7.tgz",



      "integrity": "sha512-CHOUy7mu3lbD6o6LJLfllpjkzhHXSBlX8B9+qPddUsIfeF5S/UZ5q0kmCsnRqT1UHFQZchNFDDzMbQsuesHWlw==",



      "license": "MIT",



      "dependencies": {



        "use-sync-external-store": "^1.2.2"



      },



      "engines": {



        "node": ">=12.7.0"



      },



      "peerDependencies": {



        "@types/react": ">=16.8",



        "immer": ">=9.0.6",



        "react": ">=16.8"



      },



      "peerDependenciesMeta": {



        "@types/react": {



          "optional": true



        },



        "immer": {



          "optional": true



        },



        "react": {



          "optional": true



        }



      }



    },



    "node_modules/zwitch": {



      "version": "2.0.4",



      "resolved": "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz",



      "integrity": "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==",



      "license": "MIT",



      "funding": {



        "type": "github",



        "url": "https://github.com/sponsors/wooorm"



      }



    }



  }



}



=======
{

  "name": "privacy-ai-assistant",

  "version": "0.1.0",

  "lockfileVersion": 3,

  "requires": true,

  "packages": {

    "": {

      "name": "privacy-ai-assistant",

      "version": "0.1.0",

      "license": "MIT",

      "dependencies": {

        "@tauri-apps/api": "^2.0.0",

        "@tauri-apps/plugin-fs": "^2.4.0",

        "@tauri-apps/plugin-http": "^2.3.0",

        "@tauri-apps/plugin-shell": "^2.3.0",

        "@tauri-apps/plugin-store": "^2.3.0",

        "clsx": "^2.0.0",

        "lucide-react": "^0.300.0",

        "react": "^18.2.0",

        "react-dom": "^18.2.0",

        "react-markdown": "^9.0.1",

        "tailwind-merge": "^2.2.0",

        "zustand": "^4.4.7"

      },

      "devDependencies": {

        "@tauri-apps/cli": "^2.0.0",

        "@types/react": "^18.2.43",

        "@types/react-dom": "^18.2.17",

        "@typescript-eslint/eslint-plugin": "^6.14.0",

        "@typescript-eslint/parser": "^6.14.0",

        "@vitejs/plugin-react": "^4.2.1",

        "autoprefixer": "^10.4.16",

        "eslint": "^8.55.0",

        "eslint-plugin-react-hooks": "^4.6.0",

        "eslint-plugin-react-refresh": "^0.4.5",

        "postcss": "^8.4.32",

        "prettier": "^3.1.1",

        "tailwindcss": "^3.3.6",

        "typescript": "^5.2.2",

        "vite": "^5.0.8"

      },

      "engines": {

        "node": ">=18.0.0"

      }

    },

    "node_modules/@alloc/quick-lru": {

      "version": "5.2.0",

      "resolved": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz",

      "integrity": "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/@ampproject/remapping": {

      "version": "2.3.0",

      "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz",

      "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==",

      "dev": true,

      "license": "Apache-2.0",

      "dependencies": {

        "@jridgewell/gen-mapping": "^0.3.5",

        "@jridgewell/trace-mapping": "^0.3.24"

      },

      "engines": {

        "node": ">=6.0.0"

      }

    },

    "node_modules/@babel/code-frame": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz",

      "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/helper-validator-identifier": "^7.27.1",

        "js-tokens": "^4.0.0",

        "picocolors": "^1.1.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/compat-data": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz",

      "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/core": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz",

      "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@ampproject/remapping": "^2.2.0",

        "@babel/code-frame": "^7.27.1",

        "@babel/generator": "^7.28.0",

        "@babel/helper-compilation-targets": "^7.27.2",

        "@babel/helper-module-transforms": "^7.27.3",

        "@babel/helpers": "^7.27.6",

        "@babel/parser": "^7.28.0",

        "@babel/template": "^7.27.2",

        "@babel/traverse": "^7.28.0",

        "@babel/types": "^7.28.0",

        "convert-source-map": "^2.0.0",

        "debug": "^4.1.0",

        "gensync": "^1.0.0-beta.2",

        "json5": "^2.2.3",

        "semver": "^6.3.1"

      },

      "engines": {

        "node": ">=6.9.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/babel"

      }

    },

    "node_modules/@babel/core/node_modules/semver": {

      "version": "6.3.1",

      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",

      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",

      "dev": true,

      "license": "ISC",

      "bin": {

        "semver": "bin/semver.js"

      }

    },

    "node_modules/@babel/generator": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz",

      "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/parser": "^7.28.0",

        "@babel/types": "^7.28.0",

        "@jridgewell/gen-mapping": "^0.3.12",

        "@jridgewell/trace-mapping": "^0.3.28",

        "jsesc": "^3.0.2"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-compilation-targets": {

      "version": "7.27.2",

      "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz",

      "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/compat-data": "^7.27.2",

        "@babel/helper-validator-option": "^7.27.1",

        "browserslist": "^4.24.0",

        "lru-cache": "^5.1.1",

        "semver": "^6.3.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-compilation-targets/node_modules/semver": {

      "version": "6.3.1",

      "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz",

      "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==",

      "dev": true,

      "license": "ISC",

      "bin": {

        "semver": "bin/semver.js"

      }

    },

    "node_modules/@babel/helper-globals": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz",

      "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-module-imports": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz",

      "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/traverse": "^7.27.1",

        "@babel/types": "^7.27.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-module-transforms": {

      "version": "7.27.3",

      "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz",

      "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/helper-module-imports": "^7.27.1",

        "@babel/helper-validator-identifier": "^7.27.1",

        "@babel/traverse": "^7.27.3"

      },

      "engines": {

        "node": ">=6.9.0"

      },

      "peerDependencies": {

        "@babel/core": "^7.0.0"

      }

    },

    "node_modules/@babel/helper-plugin-utils": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz",

      "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-string-parser": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz",

      "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-validator-identifier": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz",

      "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helper-validator-option": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz",

      "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/helpers": {

      "version": "7.27.6",

      "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz",

      "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/template": "^7.27.2",

        "@babel/types": "^7.27.6"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/parser": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz",

      "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/types": "^7.28.0"

      },

      "bin": {

        "parser": "bin/babel-parser.js"

      },

      "engines": {

        "node": ">=6.0.0"

      }

    },

    "node_modules/@babel/plugin-transform-react-jsx-self": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz",

      "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/helper-plugin-utils": "^7.27.1"

      },

      "engines": {

        "node": ">=6.9.0"

      },

      "peerDependencies": {

        "@babel/core": "^7.0.0-0"

      }

    },

    "node_modules/@babel/plugin-transform-react-jsx-source": {

      "version": "7.27.1",

      "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz",

      "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/helper-plugin-utils": "^7.27.1"

      },

      "engines": {

        "node": ">=6.9.0"

      },

      "peerDependencies": {

        "@babel/core": "^7.0.0-0"

      }

    },

    "node_modules/@babel/template": {

      "version": "7.27.2",

      "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz",

      "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/code-frame": "^7.27.1",

        "@babel/parser": "^7.27.2",

        "@babel/types": "^7.27.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/traverse": {

      "version": "7.28.0",

      "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz",

      "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/code-frame": "^7.27.1",

        "@babel/generator": "^7.28.0",

        "@babel/helper-globals": "^7.28.0",

        "@babel/parser": "^7.28.0",

        "@babel/template": "^7.27.2",

        "@babel/types": "^7.28.0",

        "debug": "^4.3.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@babel/types": {

      "version": "7.28.1",

      "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz",

      "integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/helper-string-parser": "^7.27.1",

        "@babel/helper-validator-identifier": "^7.27.1"

      },

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/@esbuild/aix-ppc64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz",

      "integrity": "sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==",

      "cpu": [

        "ppc64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "aix"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/android-arm": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.5.tgz",

      "integrity": "sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "android"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/android-arm64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz",

      "integrity": "sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "android"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/android-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.21.5.tgz",

      "integrity": "sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "android"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/darwin-arm64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz",

      "integrity": "sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "darwin"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/darwin-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz",

      "integrity": "sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "darwin"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/freebsd-arm64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz",

      "integrity": "sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "freebsd"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/freebsd-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz",

      "integrity": "sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "freebsd"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-arm": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz",

      "integrity": "sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-arm64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz",

      "integrity": "sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-ia32": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz",

      "integrity": "sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==",

      "cpu": [

        "ia32"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-loong64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz",

      "integrity": "sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==",

      "cpu": [

        "loong64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-mips64el": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz",

      "integrity": "sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==",

      "cpu": [

        "mips64el"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-ppc64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz",

      "integrity": "sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==",

      "cpu": [

        "ppc64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-riscv64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz",

      "integrity": "sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==",

      "cpu": [

        "riscv64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-s390x": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz",

      "integrity": "sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==",

      "cpu": [

        "s390x"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/linux-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz",

      "integrity": "sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/netbsd-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz",

      "integrity": "sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "netbsd"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/openbsd-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz",

      "integrity": "sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "openbsd"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/sunos-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz",

      "integrity": "sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "sunos"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/win32-arm64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz",

      "integrity": "sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/win32-ia32": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz",

      "integrity": "sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==",

      "cpu": [

        "ia32"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@esbuild/win32-x64": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz",

      "integrity": "sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@eslint-community/eslint-utils": {

      "version": "4.7.0",

      "resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz",

      "integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "eslint-visitor-keys": "^3.4.3"

      },

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      },

      "peerDependencies": {

        "eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"

      }

    },

    "node_modules/@eslint-community/regexpp": {

      "version": "4.12.1",

      "resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz",

      "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": "^12.0.0 || ^14.0.0 || >=16.0.0"

      }

    },

    "node_modules/@eslint/eslintrc": {

      "version": "2.1.4",

      "resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz",

      "integrity": "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ajv": "^6.12.4",

        "debug": "^4.3.2",

        "espree": "^9.6.0",

        "globals": "^13.19.0",

        "ignore": "^5.2.0",

        "import-fresh": "^3.2.1",

        "js-yaml": "^4.1.0",

        "minimatch": "^3.1.2",

        "strip-json-comments": "^3.1.1"

      },

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      }

    },

    "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {

      "version": "1.1.12",

      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",

      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "balanced-match": "^1.0.0",

        "concat-map": "0.0.1"

      }

    },

    "node_modules/@eslint/eslintrc/node_modules/minimatch": {

      "version": "3.1.2",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",

      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^1.1.7"

      },

      "engines": {

        "node": "*"

      }

    },

    "node_modules/@eslint/js": {

      "version": "8.57.1",

      "resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz",

      "integrity": "sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      }

    },

    "node_modules/@humanwhocodes/config-array": {

      "version": "0.13.0",

      "resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz",

      "integrity": "sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==",

      "deprecated": "Use @eslint/config-array instead",

      "dev": true,

      "license": "Apache-2.0",

      "dependencies": {

        "@humanwhocodes/object-schema": "^2.0.3",

        "debug": "^4.3.1",

        "minimatch": "^3.0.5"

      },

      "engines": {

        "node": ">=10.10.0"

      }

    },

    "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {

      "version": "1.1.12",

      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",

      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "balanced-match": "^1.0.0",

        "concat-map": "0.0.1"

      }

    },

    "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {

      "version": "3.1.2",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",

      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^1.1.7"

      },

      "engines": {

        "node": "*"

      }

    },

    "node_modules/@humanwhocodes/module-importer": {

      "version": "1.0.1",

      "resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz",

      "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==",

      "dev": true,

      "license": "Apache-2.0",

      "engines": {

        "node": ">=12.22"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/nzakas"

      }

    },

    "node_modules/@humanwhocodes/object-schema": {

      "version": "2.0.3",

      "resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz",

      "integrity": "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==",

      "deprecated": "Use @eslint/object-schema instead",

      "dev": true,

      "license": "BSD-3-Clause"

    },

    "node_modules/@isaacs/cliui": {

      "version": "8.0.2",

      "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz",

      "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "string-width": "^5.1.2",

        "string-width-cjs": "npm:string-width@^4.2.0",

        "strip-ansi": "^7.0.1",

        "strip-ansi-cjs": "npm:strip-ansi@^6.0.1",

        "wrap-ansi": "^8.1.0",

        "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"

      },

      "engines": {

        "node": ">=12"

      }

    },

    "node_modules/@isaacs/cliui/node_modules/ansi-regex": {

      "version": "6.1.0",

      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",

      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/ansi-regex?sponsor=1"

      }

    },

    "node_modules/@isaacs/cliui/node_modules/strip-ansi": {

      "version": "7.1.0",

      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",

      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-regex": "^6.0.1"

      },

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/strip-ansi?sponsor=1"

      }

    },

    "node_modules/@jridgewell/gen-mapping": {

      "version": "0.3.12",

      "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz",

      "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@jridgewell/sourcemap-codec": "^1.5.0",

        "@jridgewell/trace-mapping": "^0.3.24"

      }

    },

    "node_modules/@jridgewell/resolve-uri": {

      "version": "3.1.2",

      "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",

      "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.0.0"

      }

    },

    "node_modules/@jridgewell/sourcemap-codec": {

      "version": "1.5.4",

      "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz",

      "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/@jridgewell/trace-mapping": {

      "version": "0.3.29",

      "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz",

      "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@jridgewell/resolve-uri": "^3.1.0",

        "@jridgewell/sourcemap-codec": "^1.4.14"

      }

    },

    "node_modules/@nodelib/fs.scandir": {

      "version": "2.1.5",

      "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",

      "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@nodelib/fs.stat": "2.0.5",

        "run-parallel": "^1.1.9"

      },

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/@nodelib/fs.stat": {

      "version": "2.0.5",

      "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",

      "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/@nodelib/fs.walk": {

      "version": "1.2.8",

      "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",

      "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@nodelib/fs.scandir": "2.1.5",

        "fastq": "^1.6.0"

      },

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/@pkgjs/parseargs": {

      "version": "0.11.0",

      "resolved": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz",

      "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==",

      "dev": true,

      "license": "MIT",

      "optional": true,

      "engines": {

        "node": ">=14"

      }

    },

    "node_modules/@rolldown/pluginutils": {

      "version": "1.0.0-beta.19",

      "resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz",

      "integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/@rollup/rollup-android-arm-eabi": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.45.1.tgz",

      "integrity": "sha512-NEySIFvMY0ZQO+utJkgoMiCAjMrGvnbDLHvcmlA33UXJpYBCvlBEbMMtV837uCkS+plG2umfhn0T5mMAxGrlRA==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "android"

      ]

    },

    "node_modules/@rollup/rollup-android-arm64": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.45.1.tgz",

      "integrity": "sha512-ujQ+sMXJkg4LRJaYreaVx7Z/VMgBBd89wGS4qMrdtfUFZ+TSY5Rs9asgjitLwzeIbhwdEhyj29zhst3L1lKsRQ==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "android"

      ]

    },

    "node_modules/@rollup/rollup-darwin-arm64": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.45.1.tgz",

      "integrity": "sha512-FSncqHvqTm3lC6Y13xncsdOYfxGSLnP+73k815EfNmpewPs+EyM49haPS105Rh4aF5mJKywk9X0ogzLXZzN9lA==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "darwin"

      ]

    },

    "node_modules/@rollup/rollup-darwin-x64": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.45.1.tgz",

      "integrity": "sha512-2/vVn/husP5XI7Fsf/RlhDaQJ7x9zjvC81anIVbr4b/f0xtSmXQTFcGIQ/B1cXIYM6h2nAhJkdMHTnD7OtQ9Og==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "darwin"

      ]

    },

    "node_modules/@rollup/rollup-freebsd-arm64": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.45.1.tgz",

      "integrity": "sha512-4g1kaDxQItZsrkVTdYQ0bxu4ZIQ32cotoQbmsAnW1jAE4XCMbcBPDirX5fyUzdhVCKgPcrwWuucI8yrVRBw2+g==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "freebsd"

      ]

    },

    "node_modules/@rollup/rollup-freebsd-x64": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.45.1.tgz",

      "integrity": "sha512-L/6JsfiL74i3uK1Ti2ZFSNsp5NMiM4/kbbGEcOCps99aZx3g8SJMO1/9Y0n/qKlWZfn6sScf98lEOUe2mBvW9A==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "freebsd"

      ]

    },

    "node_modules/@rollup/rollup-linux-arm-gnueabihf": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.45.1.tgz",

      "integrity": "sha512-RkdOTu2jK7brlu+ZwjMIZfdV2sSYHK2qR08FUWcIoqJC2eywHbXr0L8T/pONFwkGukQqERDheaGTeedG+rra6Q==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-arm-musleabihf": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.45.1.tgz",

      "integrity": "sha512-3kJ8pgfBt6CIIr1o+HQA7OZ9mp/zDk3ctekGl9qn/pRBgrRgfwiffaUmqioUGN9hv0OHv2gxmvdKOkARCtRb8Q==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-arm64-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.45.1.tgz",

      "integrity": "sha512-k3dOKCfIVixWjG7OXTCOmDfJj3vbdhN0QYEqB+OuGArOChek22hn7Uy5A/gTDNAcCy5v2YcXRJ/Qcnm4/ma1xw==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-arm64-musl": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.45.1.tgz",

      "integrity": "sha512-PmI1vxQetnM58ZmDFl9/Uk2lpBBby6B6rF4muJc65uZbxCs0EA7hhKCk2PKlmZKuyVSHAyIw3+/SiuMLxKxWog==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-loongarch64-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.45.1.tgz",

      "integrity": "sha512-9UmI0VzGmNJ28ibHW2GpE2nF0PBQqsyiS4kcJ5vK+wuwGnV5RlqdczVocDSUfGX/Na7/XINRVoUgJyFIgipoRg==",

      "cpu": [

        "loong64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.45.1.tgz",

      "integrity": "sha512-7nR2KY8oEOUTD3pBAxIBBbZr0U7U+R9HDTPNy+5nVVHDXI4ikYniH1oxQz9VoB5PbBU1CZuDGHkLJkd3zLMWsg==",

      "cpu": [

        "ppc64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-riscv64-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.45.1.tgz",

      "integrity": "sha512-nlcl3jgUultKROfZijKjRQLUu9Ma0PeNv/VFHkZiKbXTBQXhpytS8CIj5/NfBeECZtY2FJQubm6ltIxm/ftxpw==",

      "cpu": [

        "riscv64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-riscv64-musl": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.45.1.tgz",

      "integrity": "sha512-HJV65KLS51rW0VY6rvZkiieiBnurSzpzore1bMKAhunQiECPuxsROvyeaot/tcK3A3aGnI+qTHqisrpSgQrpgA==",

      "cpu": [

        "riscv64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-s390x-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.45.1.tgz",

      "integrity": "sha512-NITBOCv3Qqc6hhwFt7jLV78VEO/il4YcBzoMGGNxznLgRQf43VQDae0aAzKiBeEPIxnDrACiMgbqjuihx08OOw==",

      "cpu": [

        "s390x"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-x64-gnu": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.45.1.tgz",

      "integrity": "sha512-+E/lYl6qu1zqgPEnTrs4WysQtvc/Sh4fC2nByfFExqgYrqkKWp1tWIbe+ELhixnenSpBbLXNi6vbEEJ8M7fiHw==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-linux-x64-musl": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.45.1.tgz",

      "integrity": "sha512-a6WIAp89p3kpNoYStITT9RbTbTnqarU7D8N8F2CV+4Cl9fwCOZraLVuVFvlpsW0SbIiYtEnhCZBPLoNdRkjQFw==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "linux"

      ]

    },

    "node_modules/@rollup/rollup-win32-arm64-msvc": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.45.1.tgz",

      "integrity": "sha512-T5Bi/NS3fQiJeYdGvRpTAP5P02kqSOpqiopwhj0uaXB6nzs5JVi2XMJb18JUSKhCOX8+UE1UKQufyD6Or48dJg==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ]

    },

    "node_modules/@rollup/rollup-win32-ia32-msvc": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.45.1.tgz",

      "integrity": "sha512-lxV2Pako3ujjuUe9jiU3/s7KSrDfH6IgTSQOnDWr9aJ92YsFd7EurmClK0ly/t8dzMkDtd04g60WX6yl0sGfdw==",

      "cpu": [

        "ia32"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ]

    },

    "node_modules/@rollup/rollup-win32-x64-msvc": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.45.1.tgz",

      "integrity": "sha512-M/fKi4sasCdM8i0aWJjCSFm2qEnYRR8AMLG2kxp6wD13+tMGA4Z1tVAuHkNRjud5SW2EM3naLuK35w9twvf6aA==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "win32"

      ]

    },

    "node_modules/@tauri-apps/api": {

      "version": "2.6.0",

      "resolved": "https://registry.npmjs.org/@tauri-apps/api/-/api-2.6.0.tgz",

      "integrity": "sha512-hRNcdercfgpzgFrMXWwNDBN0B7vNzOzRepy6ZAmhxi5mDLVPNrTpo9MGg2tN/F7JRugj4d2aF7E1rtPXAHaetg==",

      "license": "Apache-2.0 OR MIT",

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/tauri"

      }

    },

    "node_modules/@tauri-apps/cli": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-2.6.2.tgz",

      "integrity": "sha512-s1/eyBHxk0wG1blLeOY2IDjgZcxVrkxU5HFL8rNDwjYGr0o7yr3RAtwmuUPhz13NO+xGAL1bJZaLFBdp+5joKg==",

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "bin": {

        "tauri": "tauri.js"

      },

      "engines": {

        "node": ">= 10"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/tauri"

      },

      "optionalDependencies": {

        "@tauri-apps/cli-darwin-arm64": "2.6.2",

        "@tauri-apps/cli-darwin-x64": "2.6.2",

        "@tauri-apps/cli-linux-arm-gnueabihf": "2.6.2",

        "@tauri-apps/cli-linux-arm64-gnu": "2.6.2",

        "@tauri-apps/cli-linux-arm64-musl": "2.6.2",

        "@tauri-apps/cli-linux-riscv64-gnu": "2.6.2",

        "@tauri-apps/cli-linux-x64-gnu": "2.6.2",

        "@tauri-apps/cli-linux-x64-musl": "2.6.2",

        "@tauri-apps/cli-win32-arm64-msvc": "2.6.2",

        "@tauri-apps/cli-win32-ia32-msvc": "2.6.2",

        "@tauri-apps/cli-win32-x64-msvc": "2.6.2"

      }

    },

    "node_modules/@tauri-apps/cli-darwin-arm64": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-2.6.2.tgz",

      "integrity": "sha512-YlvT+Yb7u2HplyN2Cf/nBplCQARC/I4uedlYHlgtxg6rV7xbo9BvG1jLOo29IFhqA2rOp5w1LtgvVGwsOf2kxw==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "darwin"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-darwin-x64": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-2.6.2.tgz",

      "integrity": "sha512-21gdPWfv1bP8rkTdCL44in70QcYcPaDM70L+y78N8TkBuC+/+wqnHcwwjzb+mUyck6UoEw2DORagSI/oKKUGJw==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "darwin"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-arm-gnueabihf": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-2.6.2.tgz",

      "integrity": "sha512-MW8Y6HqHS5yzQkwGoLk/ZyE1tWpnz/seDoY4INsbvUZdknuUf80yn3H+s6eGKtT/0Bfqon/W9sY7pEkgHRPQgA==",

      "cpu": [

        "arm"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-arm64-gnu": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-2.6.2.tgz",

      "integrity": "sha512-9PdINTUtnyrnQt9hvC4y1m0NoxKSw/wUB9OTBAQabPj8WLAdvySWiUpEiqJjwLhlu4T6ltXZRpNTEzous3/RXg==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-arm64-musl": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-2.6.2.tgz",

      "integrity": "sha512-LrcJTRr7FrtQlTDkYaRXIGo/8YU/xkWmBPC646WwKNZ/S6yqCiDcOMoPe7Cx4ZvcG6sK6LUCLQMfaSNEL7PT0A==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-riscv64-gnu": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-riscv64-gnu/-/cli-linux-riscv64-gnu-2.6.2.tgz",

      "integrity": "sha512-GnTshO/BaZ9KGIazz2EiFfXGWgLur5/pjqklRA/ck42PGdUQJhV/Ao7A7TdXPjqAzpFxNo6M/Hx0GCH2iMS7IA==",

      "cpu": [

        "riscv64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-x64-gnu": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-2.6.2.tgz",

      "integrity": "sha512-QDG3WeJD6UJekmrtVPCJRzlKgn9sGzhvD58oAw5gIU+DRovgmmG2U1jH9fS361oYGjWWO7d/KM9t0kugZzi4lQ==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-linux-x64-musl": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-2.6.2.tgz",

      "integrity": "sha512-TNVTDDtnWzuVqWBFdZ4+8ZTg17tc21v+CT5XBQ+KYCoYtCrIaHpW04fS5Tmudi+vYdBwoPDfwpKEB6LhCeFraQ==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "linux"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-win32-arm64-msvc": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-arm64-msvc/-/cli-win32-arm64-msvc-2.6.2.tgz",

      "integrity": "sha512-z77C1oa/hMLO/jM1JF39tK3M3v9nou7RsBnQoOY54z5WPcpVAbS0XdFhXB7sSN72BOiO3moDky9lQANQz6L3CA==",

      "cpu": [

        "arm64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-win32-ia32-msvc": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-2.6.2.tgz",

      "integrity": "sha512-TmD8BbzbjluBw8+QEIWUVmFa9aAluSkT1N937n1mpYLXcPbTpbunqRFiIznTwupoJNJIdtpF/t7BdZDRh5rrcg==",

      "cpu": [

        "ia32"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/cli-win32-x64-msvc": {

      "version": "2.6.2",

      "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-2.6.2.tgz",

      "integrity": "sha512-ItB8RCKk+nCmqOxOvbNtltz6x1A4QX6cSM21kj3NkpcnjT9rHSMcfyf8WVI2fkoMUJR80iqCblUX6ARxC3lj6w==",

      "cpu": [

        "x64"

      ],

      "dev": true,

      "license": "Apache-2.0 OR MIT",

      "optional": true,

      "os": [

        "win32"

      ],

      "engines": {

        "node": ">= 10"

      }

    },

    "node_modules/@tauri-apps/plugin-fs": {

      "version": "2.4.0",

      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-fs/-/plugin-fs-2.4.0.tgz",

      "integrity": "sha512-Sp8AdDcbyXyk6LD6Pmdx44SH3LPeNAvxR2TFfq/8CwqzfO1yOyV+RzT8fov0NNN7d9nvW7O7MtMAptJ42YXA5g==",

      "license": "MIT OR Apache-2.0",

      "dependencies": {

        "@tauri-apps/api": "^2.6.0"

      }

    },

    "node_modules/@tauri-apps/plugin-http": {

      "version": "2.5.0",

      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-http/-/plugin-http-2.5.0.tgz",

      "integrity": "sha512-l4M2DUIsOBIMrbj4dJZwrB4mJiB7OA/2Tj3gEbX2fjq5MOpETklJPKfDvzUTDwuq4lIKCKKykz8E8tpOgvi0EQ==",

      "license": "MIT OR Apache-2.0",

      "dependencies": {

        "@tauri-apps/api": "^2.6.0"

      }

    },

    "node_modules/@tauri-apps/plugin-shell": {

      "version": "2.3.0",

      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-shell/-/plugin-shell-2.3.0.tgz",

      "integrity": "sha512-6GIRxO2z64uxPX4CCTuhQzefvCC0ew7HjdBhMALiGw74vFBDY95VWueAHOHgNOMV4UOUAFupyidN9YulTe5xlA==",

      "license": "MIT OR Apache-2.0",

      "dependencies": {

        "@tauri-apps/api": "^2.6.0"

      }

    },

    "node_modules/@tauri-apps/plugin-store": {

      "version": "2.3.0",

      "resolved": "https://registry.npmjs.org/@tauri-apps/plugin-store/-/plugin-store-2.3.0.tgz",

      "integrity": "sha512-mre8er0nXPhyEWQzWCpUd+UnEoBQYcoA5JYlwpwOV9wcxKqlXTGfminpKsE37ic8NUb2BIZqf0QQ9/U3ib2+/A==",

      "license": "MIT OR Apache-2.0",

      "dependencies": {

        "@tauri-apps/api": "^2.6.0"

      }

    },

    "node_modules/@types/babel__core": {

      "version": "7.20.5",

      "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz",

      "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/parser": "^7.20.7",

        "@babel/types": "^7.20.7",

        "@types/babel__generator": "*",

        "@types/babel__template": "*",

        "@types/babel__traverse": "*"

      }

    },

    "node_modules/@types/babel__generator": {

      "version": "7.27.0",

      "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz",

      "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/types": "^7.0.0"

      }

    },

    "node_modules/@types/babel__template": {

      "version": "7.4.4",

      "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz",

      "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/parser": "^7.1.0",

        "@babel/types": "^7.0.0"

      }

    },

    "node_modules/@types/babel__traverse": {

      "version": "7.20.7",

      "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz",

      "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/types": "^7.20.7"

      }

    },

    "node_modules/@types/debug": {

      "version": "4.1.12",

      "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz",

      "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==",

      "license": "MIT",

      "dependencies": {

        "@types/ms": "*"

      }

    },

    "node_modules/@types/estree": {

      "version": "1.0.8",

      "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz",

      "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==",

      "license": "MIT"

    },

    "node_modules/@types/estree-jsx": {

      "version": "1.0.5",

      "resolved": "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz",

      "integrity": "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",

      "license": "MIT",

      "dependencies": {

        "@types/estree": "*"

      }

    },

    "node_modules/@types/hast": {

      "version": "3.0.4",

      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz",

      "integrity": "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "*"

      }

    },

    "node_modules/@types/json-schema": {

      "version": "7.0.15",

      "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz",

      "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/@types/mdast": {

      "version": "4.0.4",

      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz",

      "integrity": "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "*"

      }

    },

    "node_modules/@types/ms": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz",

      "integrity": "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==",

      "license": "MIT"

    },

    "node_modules/@types/prop-types": {

      "version": "15.7.15",

      "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz",

      "integrity": "sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==",

      "license": "MIT"

    },

    "node_modules/@types/react": {

      "version": "18.3.23",

      "resolved": "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz",

      "integrity": "sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==",

      "license": "MIT",

      "dependencies": {

        "@types/prop-types": "*",

        "csstype": "^3.0.2"

      }

    },

    "node_modules/@types/react-dom": {

      "version": "18.3.7",

      "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz",

      "integrity": "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==",

      "dev": true,

      "license": "MIT",

      "peerDependencies": {

        "@types/react": "^18.0.0"

      }

    },

    "node_modules/@types/semver": {

      "version": "7.7.0",

      "resolved": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz",

      "integrity": "sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/@types/unist": {

      "version": "3.0.3",

      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz",

      "integrity": "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==",

      "license": "MIT"

    },

    "node_modules/@typescript-eslint/eslint-plugin": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz",

      "integrity": "sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@eslint-community/regexpp": "^4.5.1",

        "@typescript-eslint/scope-manager": "6.21.0",

        "@typescript-eslint/type-utils": "6.21.0",

        "@typescript-eslint/utils": "6.21.0",

        "@typescript-eslint/visitor-keys": "6.21.0",

        "debug": "^4.3.4",

        "graphemer": "^1.4.0",

        "ignore": "^5.2.4",

        "natural-compare": "^1.4.0",

        "semver": "^7.5.4",

        "ts-api-utils": "^1.0.1"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      },

      "peerDependencies": {

        "@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha",

        "eslint": "^7.0.0 || ^8.0.0"

      },

      "peerDependenciesMeta": {

        "typescript": {

          "optional": true

        }

      }

    },

    "node_modules/@typescript-eslint/parser": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.21.0.tgz",

      "integrity": "sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "@typescript-eslint/scope-manager": "6.21.0",

        "@typescript-eslint/types": "6.21.0",

        "@typescript-eslint/typescript-estree": "6.21.0",

        "@typescript-eslint/visitor-keys": "6.21.0",

        "debug": "^4.3.4"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      },

      "peerDependencies": {

        "eslint": "^7.0.0 || ^8.0.0"

      },

      "peerDependenciesMeta": {

        "typescript": {

          "optional": true

        }

      }

    },

    "node_modules/@typescript-eslint/scope-manager": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz",

      "integrity": "sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@typescript-eslint/types": "6.21.0",

        "@typescript-eslint/visitor-keys": "6.21.0"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      }

    },

    "node_modules/@typescript-eslint/type-utils": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz",

      "integrity": "sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@typescript-eslint/typescript-estree": "6.21.0",

        "@typescript-eslint/utils": "6.21.0",

        "debug": "^4.3.4",

        "ts-api-utils": "^1.0.1"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      },

      "peerDependencies": {

        "eslint": "^7.0.0 || ^8.0.0"

      },

      "peerDependenciesMeta": {

        "typescript": {

          "optional": true

        }

      }

    },

    "node_modules/@typescript-eslint/types": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/types/-/types-6.21.0.tgz",

      "integrity": "sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      }

    },

    "node_modules/@typescript-eslint/typescript-estree": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz",

      "integrity": "sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "@typescript-eslint/types": "6.21.0",

        "@typescript-eslint/visitor-keys": "6.21.0",

        "debug": "^4.3.4",

        "globby": "^11.1.0",

        "is-glob": "^4.0.3",

        "minimatch": "9.0.3",

        "semver": "^7.5.4",

        "ts-api-utils": "^1.0.1"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      },

      "peerDependenciesMeta": {

        "typescript": {

          "optional": true

        }

      }

    },

    "node_modules/@typescript-eslint/utils": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-6.21.0.tgz",

      "integrity": "sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@eslint-community/eslint-utils": "^4.4.0",

        "@types/json-schema": "^7.0.12",

        "@types/semver": "^7.5.0",

        "@typescript-eslint/scope-manager": "6.21.0",

        "@typescript-eslint/types": "6.21.0",

        "@typescript-eslint/typescript-estree": "6.21.0",

        "semver": "^7.5.4"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      },

      "peerDependencies": {

        "eslint": "^7.0.0 || ^8.0.0"

      }

    },

    "node_modules/@typescript-eslint/visitor-keys": {

      "version": "6.21.0",

      "resolved": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz",

      "integrity": "sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@typescript-eslint/types": "6.21.0",

        "eslint-visitor-keys": "^3.4.1"

      },

      "engines": {

        "node": "^16.0.0 || >=18.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/typescript-eslint"

      }

    },

    "node_modules/@ungap/structured-clone": {

      "version": "1.3.0",

      "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz",

      "integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==",

      "license": "ISC"

    },

    "node_modules/@vitejs/plugin-react": {

      "version": "4.6.0",

      "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz",

      "integrity": "sha512-5Kgff+m8e2PB+9j51eGHEpn5kUzRKH2Ry0qGoe8ItJg7pqnkPrYPkDQZGgGmTa0EGarHrkjLvOdU3b1fzI8otQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@babel/core": "^7.27.4",

        "@babel/plugin-transform-react-jsx-self": "^7.27.1",

        "@babel/plugin-transform-react-jsx-source": "^7.27.1",

        "@rolldown/pluginutils": "1.0.0-beta.19",

        "@types/babel__core": "^7.20.5",

        "react-refresh": "^0.17.0"

      },

      "engines": {

        "node": "^14.18.0 || >=16.0.0"

      },

      "peerDependencies": {

        "vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"

      }

    },

    "node_modules/acorn": {

      "version": "8.15.0",

      "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz",

      "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "acorn": "bin/acorn"

      },

      "engines": {

        "node": ">=0.4.0"

      }

    },

    "node_modules/acorn-jsx": {

      "version": "5.3.2",

      "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz",

      "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==",

      "dev": true,

      "license": "MIT",

      "peerDependencies": {

        "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"

      }

    },

    "node_modules/ajv": {

      "version": "6.12.6",

      "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz",

      "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "fast-deep-equal": "^3.1.1",

        "fast-json-stable-stringify": "^2.0.0",

        "json-schema-traverse": "^0.4.1",

        "uri-js": "^4.2.2"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/epoberezkin"

      }

    },

    "node_modules/ansi-regex": {

      "version": "5.0.1",

      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",

      "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/ansi-styles": {

      "version": "4.3.0",

      "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz",

      "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "color-convert": "^2.0.1"

      },

      "engines": {

        "node": ">=8"

      },

      "funding": {

        "url": "https://github.com/chalk/ansi-styles?sponsor=1"

      }

    },

    "node_modules/any-promise": {

      "version": "1.3.0",

      "resolved": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz",

      "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/anymatch": {

      "version": "3.1.3",

      "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",

      "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "normalize-path": "^3.0.0",

        "picomatch": "^2.0.4"

      },

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/arg": {

      "version": "5.0.2",

      "resolved": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz",

      "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/argparse": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",

      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",

      "dev": true,

      "license": "Python-2.0"

    },

    "node_modules/array-union": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz",

      "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/autoprefixer": {

      "version": "10.4.21",

      "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz",

      "integrity": "sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/postcss/"

        },

        {

          "type": "tidelift",

          "url": "https://tidelift.com/funding/github/npm/autoprefixer"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "browserslist": "^4.24.4",

        "caniuse-lite": "^1.0.30001702",

        "fraction.js": "^4.3.7",

        "normalize-range": "^0.1.2",

        "picocolors": "^1.1.1",

        "postcss-value-parser": "^4.2.0"

      },

      "bin": {

        "autoprefixer": "bin/autoprefixer"

      },

      "engines": {

        "node": "^10 || ^12 || >=14"

      },

      "peerDependencies": {

        "postcss": "^8.1.0"

      }

    },

    "node_modules/bail": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz",

      "integrity": "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/balanced-match": {

      "version": "1.0.2",

      "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",

      "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/binary-extensions": {

      "version": "2.3.0",

      "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz",

      "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/brace-expansion": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz",

      "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "balanced-match": "^1.0.0"

      }

    },

    "node_modules/braces": {

      "version": "3.0.3",

      "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",

      "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "fill-range": "^7.1.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/browserslist": {

      "version": "4.25.1",

      "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz",

      "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/browserslist"

        },

        {

          "type": "tidelift",

          "url": "https://tidelift.com/funding/github/npm/browserslist"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "caniuse-lite": "^1.0.30001726",

        "electron-to-chromium": "^1.5.173",

        "node-releases": "^2.0.19",

        "update-browserslist-db": "^1.1.3"

      },

      "bin": {

        "browserslist": "cli.js"

      },

      "engines": {

        "node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"

      }

    },

    "node_modules/callsites": {

      "version": "3.1.0",

      "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz",

      "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/camelcase-css": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz",

      "integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/caniuse-lite": {

      "version": "1.0.30001727",

      "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz",

      "integrity": "sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/browserslist"

        },

        {

          "type": "tidelift",

          "url": "https://tidelift.com/funding/github/npm/caniuse-lite"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "CC-BY-4.0"

    },

    "node_modules/ccount": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz",

      "integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/chalk": {

      "version": "4.1.2",

      "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz",

      "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-styles": "^4.1.0",

        "supports-color": "^7.1.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/chalk/chalk?sponsor=1"

      }

    },

    "node_modules/character-entities": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz",

      "integrity": "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/character-entities-html4": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz",

      "integrity": "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/character-entities-legacy": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz",

      "integrity": "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/character-reference-invalid": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz",

      "integrity": "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/chokidar": {

      "version": "3.6.0",

      "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz",

      "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "anymatch": "~3.1.2",

        "braces": "~3.0.2",

        "glob-parent": "~5.1.2",

        "is-binary-path": "~2.1.0",

        "is-glob": "~4.0.1",

        "normalize-path": "~3.0.0",

        "readdirp": "~3.6.0"

      },

      "engines": {

        "node": ">= 8.10.0"

      },

      "funding": {

        "url": "https://paulmillr.com/funding/"

      },

      "optionalDependencies": {

        "fsevents": "~2.3.2"

      }

    },

    "node_modules/chokidar/node_modules/glob-parent": {

      "version": "5.1.2",

      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",

      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "is-glob": "^4.0.1"

      },

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/clsx": {

      "version": "2.1.1",

      "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",

      "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",

      "license": "MIT",

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/color-convert": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",

      "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "color-name": "~1.1.4"

      },

      "engines": {

        "node": ">=7.0.0"

      }

    },

    "node_modules/color-name": {

      "version": "1.1.4",

      "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",

      "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/comma-separated-tokens": {

      "version": "2.0.3",

      "resolved": "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz",

      "integrity": "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/commander": {

      "version": "4.1.1",

      "resolved": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz",

      "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/concat-map": {

      "version": "0.0.1",

      "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz",

      "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/convert-source-map": {

      "version": "2.0.0",

      "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz",

      "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/cross-spawn": {

      "version": "7.0.6",

      "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",

      "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "path-key": "^3.1.0",

        "shebang-command": "^2.0.0",

        "which": "^2.0.1"

      },

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/cssesc": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz",

      "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "cssesc": "bin/cssesc"

      },

      "engines": {

        "node": ">=4"

      }

    },

    "node_modules/csstype": {

      "version": "3.1.3",

      "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz",

      "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==",

      "license": "MIT"

    },

    "node_modules/debug": {

      "version": "4.4.1",

      "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz",

      "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==",

      "license": "MIT",

      "dependencies": {

        "ms": "^2.1.3"

      },

      "engines": {

        "node": ">=6.0"

      },

      "peerDependenciesMeta": {

        "supports-color": {

          "optional": true

        }

      }

    },

    "node_modules/decode-named-character-reference": {

      "version": "1.2.0",

      "resolved": "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.2.0.tgz",

      "integrity": "sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==",

      "license": "MIT",

      "dependencies": {

        "character-entities": "^2.0.0"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/deep-is": {

      "version": "0.1.4",

      "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz",

      "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/dequal": {

      "version": "2.0.3",

      "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz",

      "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==",

      "license": "MIT",

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/devlop": {

      "version": "1.1.0",

      "resolved": "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz",

      "integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",

      "license": "MIT",

      "dependencies": {

        "dequal": "^2.0.0"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/didyoumean": {

      "version": "1.2.2",

      "resolved": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz",

      "integrity": "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==",

      "dev": true,

      "license": "Apache-2.0"

    },

    "node_modules/dir-glob": {

      "version": "3.0.1",

      "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz",

      "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "path-type": "^4.0.0"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/dlv": {

      "version": "1.1.3",

      "resolved": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz",

      "integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/doctrine": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz",

      "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==",

      "dev": true,

      "license": "Apache-2.0",

      "dependencies": {

        "esutils": "^2.0.2"

      },

      "engines": {

        "node": ">=6.0.0"

      }

    },

    "node_modules/eastasianwidth": {

      "version": "0.2.0",

      "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz",

      "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/electron-to-chromium": {

      "version": "1.5.186",

      "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.186.tgz",

      "integrity": "sha512-lur7L4BFklgepaJxj4DqPk7vKbTEl0pajNlg2QjE5shefmlmBLm2HvQ7PMf1R/GvlevT/581cop33/quQcfX3A==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/emoji-regex": {

      "version": "9.2.2",

      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz",

      "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/esbuild": {

      "version": "0.21.5",

      "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.21.5.tgz",

      "integrity": "sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==",

      "dev": true,

      "hasInstallScript": true,

      "license": "MIT",

      "bin": {

        "esbuild": "bin/esbuild"

      },

      "engines": {

        "node": ">=12"

      },

      "optionalDependencies": {

        "@esbuild/aix-ppc64": "0.21.5",

        "@esbuild/android-arm": "0.21.5",

        "@esbuild/android-arm64": "0.21.5",

        "@esbuild/android-x64": "0.21.5",

        "@esbuild/darwin-arm64": "0.21.5",

        "@esbuild/darwin-x64": "0.21.5",

        "@esbuild/freebsd-arm64": "0.21.5",

        "@esbuild/freebsd-x64": "0.21.5",

        "@esbuild/linux-arm": "0.21.5",

        "@esbuild/linux-arm64": "0.21.5",

        "@esbuild/linux-ia32": "0.21.5",

        "@esbuild/linux-loong64": "0.21.5",

        "@esbuild/linux-mips64el": "0.21.5",

        "@esbuild/linux-ppc64": "0.21.5",

        "@esbuild/linux-riscv64": "0.21.5",

        "@esbuild/linux-s390x": "0.21.5",

        "@esbuild/linux-x64": "0.21.5",

        "@esbuild/netbsd-x64": "0.21.5",

        "@esbuild/openbsd-x64": "0.21.5",

        "@esbuild/sunos-x64": "0.21.5",

        "@esbuild/win32-arm64": "0.21.5",

        "@esbuild/win32-ia32": "0.21.5",

        "@esbuild/win32-x64": "0.21.5"

      }

    },

    "node_modules/escalade": {

      "version": "3.2.0",

      "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz",

      "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/escape-string-regexp": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz",

      "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/eslint": {

      "version": "8.57.1",

      "resolved": "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz",

      "integrity": "sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==",

      "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@eslint-community/eslint-utils": "^4.2.0",

        "@eslint-community/regexpp": "^4.6.1",

        "@eslint/eslintrc": "^2.1.4",

        "@eslint/js": "8.57.1",

        "@humanwhocodes/config-array": "^0.13.0",

        "@humanwhocodes/module-importer": "^1.0.1",

        "@nodelib/fs.walk": "^1.2.8",

        "@ungap/structured-clone": "^1.2.0",

        "ajv": "^6.12.4",

        "chalk": "^4.0.0",

        "cross-spawn": "^7.0.2",

        "debug": "^4.3.2",

        "doctrine": "^3.0.0",

        "escape-string-regexp": "^4.0.0",

        "eslint-scope": "^7.2.2",

        "eslint-visitor-keys": "^3.4.3",

        "espree": "^9.6.1",

        "esquery": "^1.4.2",

        "esutils": "^2.0.2",

        "fast-deep-equal": "^3.1.3",

        "file-entry-cache": "^6.0.1",

        "find-up": "^5.0.0",

        "glob-parent": "^6.0.2",

        "globals": "^13.19.0",

        "graphemer": "^1.4.0",

        "ignore": "^5.2.0",

        "imurmurhash": "^0.1.4",

        "is-glob": "^4.0.0",

        "is-path-inside": "^3.0.3",

        "js-yaml": "^4.1.0",

        "json-stable-stringify-without-jsonify": "^1.0.1",

        "levn": "^0.4.1",

        "lodash.merge": "^4.6.2",

        "minimatch": "^3.1.2",

        "natural-compare": "^1.4.0",

        "optionator": "^0.9.3",

        "strip-ansi": "^6.0.1",

        "text-table": "^0.2.0"

      },

      "bin": {

        "eslint": "bin/eslint.js"

      },

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      }

    },

    "node_modules/eslint-plugin-react-hooks": {

      "version": "4.6.2",

      "resolved": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz",

      "integrity": "sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=10"

      },

      "peerDependencies": {

        "eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"

      }

    },

    "node_modules/eslint-plugin-react-refresh": {

      "version": "0.4.20",

      "resolved": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz",

      "integrity": "sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==",

      "dev": true,

      "license": "MIT",

      "peerDependencies": {

        "eslint": ">=8.40"

      }

    },

    "node_modules/eslint-scope": {

      "version": "7.2.2",

      "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz",

      "integrity": "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "esrecurse": "^4.3.0",

        "estraverse": "^5.2.0"

      },

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      }

    },

    "node_modules/eslint-visitor-keys": {

      "version": "3.4.3",

      "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz",

      "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==",

      "dev": true,

      "license": "Apache-2.0",

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      }

    },

    "node_modules/eslint/node_modules/brace-expansion": {

      "version": "1.1.12",

      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",

      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "balanced-match": "^1.0.0",

        "concat-map": "0.0.1"

      }

    },

    "node_modules/eslint/node_modules/minimatch": {

      "version": "3.1.2",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",

      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^1.1.7"

      },

      "engines": {

        "node": "*"

      }

    },

    "node_modules/espree": {

      "version": "9.6.1",

      "resolved": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz",

      "integrity": "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "acorn": "^8.9.0",

        "acorn-jsx": "^5.3.2",

        "eslint-visitor-keys": "^3.4.1"

      },

      "engines": {

        "node": "^12.22.0 || ^14.17.0 || >=16.0.0"

      },

      "funding": {

        "url": "https://opencollective.com/eslint"

      }

    },

    "node_modules/esquery": {

      "version": "1.6.0",

      "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz",

      "integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==",

      "dev": true,

      "license": "BSD-3-Clause",

      "dependencies": {

        "estraverse": "^5.1.0"

      },

      "engines": {

        "node": ">=0.10"

      }

    },

    "node_modules/esrecurse": {

      "version": "4.3.0",

      "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz",

      "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "estraverse": "^5.2.0"

      },

      "engines": {

        "node": ">=4.0"

      }

    },

    "node_modules/estraverse": {

      "version": "5.3.0",

      "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz",

      "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==",

      "dev": true,

      "license": "BSD-2-Clause",

      "engines": {

        "node": ">=4.0"

      }

    },

    "node_modules/estree-util-is-identifier-name": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz",

      "integrity": "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==",

      "license": "MIT",

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/esutils": {

      "version": "2.0.3",

      "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz",

      "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==",

      "dev": true,

      "license": "BSD-2-Clause",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/extend": {

      "version": "3.0.2",

      "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz",

      "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==",

      "license": "MIT"

    },

    "node_modules/fast-deep-equal": {

      "version": "3.1.3",

      "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz",

      "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/fast-glob": {

      "version": "3.3.3",

      "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",

      "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@nodelib/fs.stat": "^2.0.2",

        "@nodelib/fs.walk": "^1.2.3",

        "glob-parent": "^5.1.2",

        "merge2": "^1.3.0",

        "micromatch": "^4.0.8"

      },

      "engines": {

        "node": ">=8.6.0"

      }

    },

    "node_modules/fast-glob/node_modules/glob-parent": {

      "version": "5.1.2",

      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",

      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "is-glob": "^4.0.1"

      },

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/fast-json-stable-stringify": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz",

      "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/fast-levenshtein": {

      "version": "2.0.6",

      "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz",

      "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/fastq": {

      "version": "1.19.1",

      "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz",

      "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "reusify": "^1.0.4"

      }

    },

    "node_modules/file-entry-cache": {

      "version": "6.0.1",

      "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz",

      "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "flat-cache": "^3.0.4"

      },

      "engines": {

        "node": "^10.12.0 || >=12.0.0"

      }

    },

    "node_modules/fill-range": {

      "version": "7.1.1",

      "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",

      "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "to-regex-range": "^5.0.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/find-up": {

      "version": "5.0.0",

      "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz",

      "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "locate-path": "^6.0.0",

        "path-exists": "^4.0.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/flat-cache": {

      "version": "3.2.0",

      "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz",

      "integrity": "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "flatted": "^3.2.9",

        "keyv": "^4.5.3",

        "rimraf": "^3.0.2"

      },

      "engines": {

        "node": "^10.12.0 || >=12.0.0"

      }

    },

    "node_modules/flatted": {

      "version": "3.3.3",

      "resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz",

      "integrity": "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/foreground-child": {

      "version": "3.3.1",

      "resolved": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz",

      "integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "cross-spawn": "^7.0.6",

        "signal-exit": "^4.0.1"

      },

      "engines": {

        "node": ">=14"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/fraction.js": {

      "version": "4.3.7",

      "resolved": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz",

      "integrity": "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": "*"

      },

      "funding": {

        "type": "patreon",

        "url": "https://github.com/sponsors/rawify"

      }

    },

    "node_modules/fs.realpath": {

      "version": "1.0.0",

      "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz",

      "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/fsevents": {

      "version": "2.3.3",

      "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",

      "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",

      "dev": true,

      "hasInstallScript": true,

      "license": "MIT",

      "optional": true,

      "os": [

        "darwin"

      ],

      "engines": {

        "node": "^8.16.0 || ^10.6.0 || >=11.0.0"

      }

    },

    "node_modules/function-bind": {

      "version": "1.1.2",

      "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",

      "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",

      "dev": true,

      "license": "MIT",

      "funding": {

        "url": "https://github.com/sponsors/ljharb"

      }

    },

    "node_modules/gensync": {

      "version": "1.0.0-beta.2",

      "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz",

      "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6.9.0"

      }

    },

    "node_modules/glob": {

      "version": "7.2.3",

      "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz",

      "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==",

      "deprecated": "Glob versions prior to v9 are no longer supported",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "fs.realpath": "^1.0.0",

        "inflight": "^1.0.4",

        "inherits": "2",

        "minimatch": "^3.1.1",

        "once": "^1.3.0",

        "path-is-absolute": "^1.0.0"

      },

      "engines": {

        "node": "*"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/glob-parent": {

      "version": "6.0.2",

      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz",

      "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "is-glob": "^4.0.3"

      },

      "engines": {

        "node": ">=10.13.0"

      }

    },

    "node_modules/glob/node_modules/brace-expansion": {

      "version": "1.1.12",

      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz",

      "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "balanced-match": "^1.0.0",

        "concat-map": "0.0.1"

      }

    },

    "node_modules/glob/node_modules/minimatch": {

      "version": "3.1.2",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",

      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^1.1.7"

      },

      "engines": {

        "node": "*"

      }

    },

    "node_modules/globals": {

      "version": "13.24.0",

      "resolved": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz",

      "integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "type-fest": "^0.20.2"

      },

      "engines": {

        "node": ">=8"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/globby": {

      "version": "11.1.0",

      "resolved": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz",

      "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "array-union": "^2.1.0",

        "dir-glob": "^3.0.1",

        "fast-glob": "^3.2.9",

        "ignore": "^5.2.0",

        "merge2": "^1.4.1",

        "slash": "^3.0.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/graphemer": {

      "version": "1.4.0",

      "resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz",

      "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/has-flag": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz",

      "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/hasown": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",

      "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "function-bind": "^1.1.2"

      },

      "engines": {

        "node": ">= 0.4"

      }

    },

    "node_modules/hast-util-to-jsx-runtime": {

      "version": "2.3.6",

      "resolved": "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz",

      "integrity": "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==",

      "license": "MIT",

      "dependencies": {

        "@types/estree": "^1.0.0",

        "@types/hast": "^3.0.0",

        "@types/unist": "^3.0.0",

        "comma-separated-tokens": "^2.0.0",

        "devlop": "^1.0.0",

        "estree-util-is-identifier-name": "^3.0.0",

        "hast-util-whitespace": "^3.0.0",

        "mdast-util-mdx-expression": "^2.0.0",

        "mdast-util-mdx-jsx": "^3.0.0",

        "mdast-util-mdxjs-esm": "^2.0.0",

        "property-information": "^7.0.0",

        "space-separated-tokens": "^2.0.0",

        "style-to-js": "^1.0.0",

        "unist-util-position": "^5.0.0",

        "vfile-message": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/hast-util-whitespace": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz",

      "integrity": "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",

      "license": "MIT",

      "dependencies": {

        "@types/hast": "^3.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/html-url-attributes": {

      "version": "3.0.1",

      "resolved": "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz",

      "integrity": "sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==",

      "license": "MIT",

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/ignore": {

      "version": "5.3.2",

      "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz",

      "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 4"

      }

    },

    "node_modules/import-fresh": {

      "version": "3.3.1",

      "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz",

      "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "parent-module": "^1.0.0",

        "resolve-from": "^4.0.0"

      },

      "engines": {

        "node": ">=6"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/imurmurhash": {

      "version": "0.1.4",

      "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz",

      "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.8.19"

      }

    },

    "node_modules/inflight": {

      "version": "1.0.6",

      "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz",

      "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==",

      "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "once": "^1.3.0",

        "wrappy": "1"

      }

    },

    "node_modules/inherits": {

      "version": "2.0.4",

      "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz",

      "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/inline-style-parser": {

      "version": "0.2.4",

      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz",

      "integrity": "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==",

      "license": "MIT"

    },

    "node_modules/is-alphabetical": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz",

      "integrity": "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/is-alphanumerical": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz",

      "integrity": "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",

      "license": "MIT",

      "dependencies": {

        "is-alphabetical": "^2.0.0",

        "is-decimal": "^2.0.0"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/is-binary-path": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",

      "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "binary-extensions": "^2.0.0"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/is-core-module": {

      "version": "2.16.1",

      "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz",

      "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "hasown": "^2.0.2"

      },

      "engines": {

        "node": ">= 0.4"

      },

      "funding": {

        "url": "https://github.com/sponsors/ljharb"

      }

    },

    "node_modules/is-decimal": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz",

      "integrity": "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/is-extglob": {

      "version": "2.1.1",

      "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",

      "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/is-fullwidth-code-point": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",

      "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/is-glob": {

      "version": "4.0.3",

      "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",

      "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "is-extglob": "^2.1.1"

      },

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/is-hexadecimal": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz",

      "integrity": "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/is-number": {

      "version": "7.0.0",

      "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",

      "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.12.0"

      }

    },

    "node_modules/is-path-inside": {

      "version": "3.0.3",

      "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz",

      "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/is-plain-obj": {

      "version": "4.1.0",

      "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",

      "integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==",

      "license": "MIT",

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/isexe": {

      "version": "2.0.0",

      "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",

      "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/jackspeak": {

      "version": "3.4.3",

      "resolved": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz",

      "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==",

      "dev": true,

      "license": "BlueOak-1.0.0",

      "dependencies": {

        "@isaacs/cliui": "^8.0.2"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      },

      "optionalDependencies": {

        "@pkgjs/parseargs": "^0.11.0"

      }

    },

    "node_modules/jiti": {

      "version": "1.21.7",

      "resolved": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz",

      "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "jiti": "bin/jiti.js"

      }

    },

    "node_modules/js-tokens": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",

      "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==",

      "license": "MIT"

    },

    "node_modules/js-yaml": {

      "version": "4.1.0",

      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz",

      "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "argparse": "^2.0.1"

      },

      "bin": {

        "js-yaml": "bin/js-yaml.js"

      }

    },

    "node_modules/jsesc": {

      "version": "3.1.0",

      "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz",

      "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "jsesc": "bin/jsesc"

      },

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/json-buffer": {

      "version": "3.0.1",

      "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz",

      "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/json-schema-traverse": {

      "version": "0.4.1",

      "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz",

      "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/json-stable-stringify-without-jsonify": {

      "version": "1.0.1",

      "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz",

      "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/json5": {

      "version": "2.2.3",

      "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz",

      "integrity": "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "json5": "lib/cli.js"

      },

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/keyv": {

      "version": "4.5.4",

      "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz",

      "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "json-buffer": "3.0.1"

      }

    },

    "node_modules/levn": {

      "version": "0.4.1",

      "resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz",

      "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "prelude-ls": "^1.2.1",

        "type-check": "~0.4.0"

      },

      "engines": {

        "node": ">= 0.8.0"

      }

    },

    "node_modules/lilconfig": {

      "version": "3.1.3",

      "resolved": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz",

      "integrity": "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=14"

      },

      "funding": {

        "url": "https://github.com/sponsors/antonk52"

      }

    },

    "node_modules/lines-and-columns": {

      "version": "1.2.4",

      "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",

      "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/locate-path": {

      "version": "6.0.0",

      "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz",

      "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "p-locate": "^5.0.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/lodash.merge": {

      "version": "4.6.2",

      "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz",

      "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/longest-streak": {

      "version": "3.1.0",

      "resolved": "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz",

      "integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/loose-envify": {

      "version": "1.4.0",

      "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",

      "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",

      "license": "MIT",

      "dependencies": {

        "js-tokens": "^3.0.0 || ^4.0.0"

      },

      "bin": {

        "loose-envify": "cli.js"

      }

    },

    "node_modules/lru-cache": {

      "version": "5.1.1",

      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz",

      "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "yallist": "^3.0.2"

      }

    },

    "node_modules/lucide-react": {

      "version": "0.300.0",

      "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.300.0.tgz",

      "integrity": "sha512-rQxUUCmWAvNLoAsMZ5j04b2+OJv6UuNLYMY7VK0eVlm4aTwUEjEEHc09/DipkNIlhXUSDn2xoyIzVT0uh7dRsg==",

      "license": "ISC",

      "peerDependencies": {

        "react": "^16.5.1 || ^17.0.0 || ^18.0.0"

      }

    },

    "node_modules/mdast-util-from-markdown": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz",

      "integrity": "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==",

      "license": "MIT",

      "dependencies": {

        "@types/mdast": "^4.0.0",

        "@types/unist": "^3.0.0",

        "decode-named-character-reference": "^1.0.0",

        "devlop": "^1.0.0",

        "mdast-util-to-string": "^4.0.0",

        "micromark": "^4.0.0",

        "micromark-util-decode-numeric-character-reference": "^2.0.0",

        "micromark-util-decode-string": "^2.0.0",

        "micromark-util-normalize-identifier": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0",

        "unist-util-stringify-position": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-mdx-expression": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz",

      "integrity": "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==",

      "license": "MIT",

      "dependencies": {

        "@types/estree-jsx": "^1.0.0",

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "devlop": "^1.0.0",

        "mdast-util-from-markdown": "^2.0.0",

        "mdast-util-to-markdown": "^2.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-mdx-jsx": {

      "version": "3.2.0",

      "resolved": "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz",

      "integrity": "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==",

      "license": "MIT",

      "dependencies": {

        "@types/estree-jsx": "^1.0.0",

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "@types/unist": "^3.0.0",

        "ccount": "^2.0.0",

        "devlop": "^1.1.0",

        "mdast-util-from-markdown": "^2.0.0",

        "mdast-util-to-markdown": "^2.0.0",

        "parse-entities": "^4.0.0",

        "stringify-entities": "^4.0.0",

        "unist-util-stringify-position": "^4.0.0",

        "vfile-message": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-mdxjs-esm": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz",

      "integrity": "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",

      "license": "MIT",

      "dependencies": {

        "@types/estree-jsx": "^1.0.0",

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "devlop": "^1.0.0",

        "mdast-util-from-markdown": "^2.0.0",

        "mdast-util-to-markdown": "^2.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-phrasing": {

      "version": "4.1.0",

      "resolved": "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz",

      "integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",

      "license": "MIT",

      "dependencies": {

        "@types/mdast": "^4.0.0",

        "unist-util-is": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-to-hast": {

      "version": "13.2.0",

      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz",

      "integrity": "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==",

      "license": "MIT",

      "dependencies": {

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "@ungap/structured-clone": "^1.0.0",

        "devlop": "^1.0.0",

        "micromark-util-sanitize-uri": "^2.0.0",

        "trim-lines": "^3.0.0",

        "unist-util-position": "^5.0.0",

        "unist-util-visit": "^5.0.0",

        "vfile": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-to-markdown": {

      "version": "2.1.2",

      "resolved": "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz",

      "integrity": "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==",

      "license": "MIT",

      "dependencies": {

        "@types/mdast": "^4.0.0",

        "@types/unist": "^3.0.0",

        "longest-streak": "^3.0.0",

        "mdast-util-phrasing": "^4.0.0",

        "mdast-util-to-string": "^4.0.0",

        "micromark-util-classify-character": "^2.0.0",

        "micromark-util-decode-string": "^2.0.0",

        "unist-util-visit": "^5.0.0",

        "zwitch": "^2.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/mdast-util-to-string": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz",

      "integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",

      "license": "MIT",

      "dependencies": {

        "@types/mdast": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/merge2": {

      "version": "1.4.1",

      "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",

      "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/micromark": {

      "version": "4.0.2",

      "resolved": "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz",

      "integrity": "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "@types/debug": "^4.0.0",

        "debug": "^4.0.0",

        "decode-named-character-reference": "^1.0.0",

        "devlop": "^1.0.0",

        "micromark-core-commonmark": "^2.0.0",

        "micromark-factory-space": "^2.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-chunked": "^2.0.0",

        "micromark-util-combine-extensions": "^2.0.0",

        "micromark-util-decode-numeric-character-reference": "^2.0.0",

        "micromark-util-encode": "^2.0.0",

        "micromark-util-normalize-identifier": "^2.0.0",

        "micromark-util-resolve-all": "^2.0.0",

        "micromark-util-sanitize-uri": "^2.0.0",

        "micromark-util-subtokenize": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-core-commonmark": {

      "version": "2.0.3",

      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz",

      "integrity": "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "decode-named-character-reference": "^1.0.0",

        "devlop": "^1.0.0",

        "micromark-factory-destination": "^2.0.0",

        "micromark-factory-label": "^2.0.0",

        "micromark-factory-space": "^2.0.0",

        "micromark-factory-title": "^2.0.0",

        "micromark-factory-whitespace": "^2.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-chunked": "^2.0.0",

        "micromark-util-classify-character": "^2.0.0",

        "micromark-util-html-tag-name": "^2.0.0",

        "micromark-util-normalize-identifier": "^2.0.0",

        "micromark-util-resolve-all": "^2.0.0",

        "micromark-util-subtokenize": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-factory-destination": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz",

      "integrity": "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-character": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-factory-label": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz",

      "integrity": "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "devlop": "^1.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-factory-space": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz",

      "integrity": "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-character": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-factory-title": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz",

      "integrity": "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-factory-space": "^2.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-factory-whitespace": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz",

      "integrity": "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-factory-space": "^2.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-character": {

      "version": "2.1.1",

      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz",

      "integrity": "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-chunked": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz",

      "integrity": "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-symbol": "^2.0.0"

      }

    },

    "node_modules/micromark-util-classify-character": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz",

      "integrity": "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-character": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-combine-extensions": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz",

      "integrity": "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-chunked": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-decode-numeric-character-reference": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz",

      "integrity": "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-symbol": "^2.0.0"

      }

    },

    "node_modules/micromark-util-decode-string": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz",

      "integrity": "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "decode-named-character-reference": "^1.0.0",

        "micromark-util-character": "^2.0.0",

        "micromark-util-decode-numeric-character-reference": "^2.0.0",

        "micromark-util-symbol": "^2.0.0"

      }

    },

    "node_modules/micromark-util-encode": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz",

      "integrity": "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT"

    },

    "node_modules/micromark-util-html-tag-name": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz",

      "integrity": "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT"

    },

    "node_modules/micromark-util-normalize-identifier": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz",

      "integrity": "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-symbol": "^2.0.0"

      }

    },

    "node_modules/micromark-util-resolve-all": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz",

      "integrity": "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-sanitize-uri": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz",

      "integrity": "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "micromark-util-character": "^2.0.0",

        "micromark-util-encode": "^2.0.0",

        "micromark-util-symbol": "^2.0.0"

      }

    },

    "node_modules/micromark-util-subtokenize": {

      "version": "2.1.0",

      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz",

      "integrity": "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "devlop": "^1.0.0",

        "micromark-util-chunked": "^2.0.0",

        "micromark-util-symbol": "^2.0.0",

        "micromark-util-types": "^2.0.0"

      }

    },

    "node_modules/micromark-util-symbol": {

      "version": "2.0.1",

      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz",

      "integrity": "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT"

    },

    "node_modules/micromark-util-types": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz",

      "integrity": "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==",

      "funding": [

        {

          "type": "GitHub Sponsors",

          "url": "https://github.com/sponsors/unifiedjs"

        },

        {

          "type": "OpenCollective",

          "url": "https://opencollective.com/unified"

        }

      ],

      "license": "MIT"

    },

    "node_modules/micromatch": {

      "version": "4.0.8",

      "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz",

      "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "braces": "^3.0.3",

        "picomatch": "^2.3.1"

      },

      "engines": {

        "node": ">=8.6"

      }

    },

    "node_modules/minimatch": {

      "version": "9.0.3",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz",

      "integrity": "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^2.0.1"

      },

      "engines": {

        "node": ">=16 || 14 >=14.17"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/minipass": {

      "version": "7.1.2",

      "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz",

      "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==",

      "dev": true,

      "license": "ISC",

      "engines": {

        "node": ">=16 || 14 >=14.17"

      }

    },

    "node_modules/ms": {

      "version": "2.1.3",

      "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",

      "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",

      "license": "MIT"

    },

    "node_modules/mz": {

      "version": "2.7.0",

      "resolved": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz",

      "integrity": "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "any-promise": "^1.0.0",

        "object-assign": "^4.0.1",

        "thenify-all": "^1.0.0"

      }

    },

    "node_modules/nanoid": {

      "version": "3.3.11",

      "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz",

      "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==",

      "dev": true,

      "funding": [

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "bin": {

        "nanoid": "bin/nanoid.cjs"

      },

      "engines": {

        "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"

      }

    },

    "node_modules/natural-compare": {

      "version": "1.4.0",

      "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz",

      "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/node-releases": {

      "version": "2.0.19",

      "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz",

      "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/normalize-path": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",

      "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/normalize-range": {

      "version": "0.1.2",

      "resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz",

      "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/object-assign": {

      "version": "4.1.1",

      "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",

      "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/object-hash": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz",

      "integrity": "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/once": {

      "version": "1.4.0",

      "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz",

      "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "wrappy": "1"

      }

    },

    "node_modules/optionator": {

      "version": "0.9.4",

      "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz",

      "integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "deep-is": "^0.1.3",

        "fast-levenshtein": "^2.0.6",

        "levn": "^0.4.1",

        "prelude-ls": "^1.2.1",

        "type-check": "^0.4.0",

        "word-wrap": "^1.2.5"

      },

      "engines": {

        "node": ">= 0.8.0"

      }

    },

    "node_modules/p-limit": {

      "version": "3.1.0",

      "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz",

      "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "yocto-queue": "^0.1.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/p-locate": {

      "version": "5.0.0",

      "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz",

      "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "p-limit": "^3.0.2"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/package-json-from-dist": {

      "version": "1.0.1",

      "resolved": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz",

      "integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==",

      "dev": true,

      "license": "BlueOak-1.0.0"

    },

    "node_modules/parent-module": {

      "version": "1.0.1",

      "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz",

      "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "callsites": "^3.0.0"

      },

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/parse-entities": {

      "version": "4.0.2",

      "resolved": "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz",

      "integrity": "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^2.0.0",

        "character-entities-legacy": "^3.0.0",

        "character-reference-invalid": "^2.0.0",

        "decode-named-character-reference": "^1.0.0",

        "is-alphanumerical": "^2.0.0",

        "is-decimal": "^2.0.0",

        "is-hexadecimal": "^2.0.0"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/parse-entities/node_modules/@types/unist": {

      "version": "2.0.11",

      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",

      "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==",

      "license": "MIT"

    },

    "node_modules/path-exists": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz",

      "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/path-is-absolute": {

      "version": "1.0.1",

      "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz",

      "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/path-key": {

      "version": "3.1.1",

      "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",

      "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/path-parse": {

      "version": "1.0.7",

      "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",

      "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/path-scurry": {

      "version": "1.11.1",

      "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz",

      "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==",

      "dev": true,

      "license": "BlueOak-1.0.0",

      "dependencies": {

        "lru-cache": "^10.2.0",

        "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"

      },

      "engines": {

        "node": ">=16 || 14 >=14.18"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/path-scurry/node_modules/lru-cache": {

      "version": "10.4.3",

      "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz",

      "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/path-type": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz",

      "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/picocolors": {

      "version": "1.1.1",

      "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz",

      "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/picomatch": {

      "version": "2.3.1",

      "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",

      "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8.6"

      },

      "funding": {

        "url": "https://github.com/sponsors/jonschlinkert"

      }

    },

    "node_modules/pify": {

      "version": "2.3.0",

      "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz",

      "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/pirates": {

      "version": "4.0.7",

      "resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz",

      "integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 6"

      }

    },

    "node_modules/postcss": {

      "version": "8.5.6",

      "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz",

      "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/postcss/"

        },

        {

          "type": "tidelift",

          "url": "https://tidelift.com/funding/github/npm/postcss"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "nanoid": "^3.3.11",

        "picocolors": "^1.1.1",

        "source-map-js": "^1.2.1"

      },

      "engines": {

        "node": "^10 || ^12 || >=14"

      }

    },

    "node_modules/postcss-import": {

      "version": "15.1.0",

      "resolved": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz",

      "integrity": "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "postcss-value-parser": "^4.0.0",

        "read-cache": "^1.0.0",

        "resolve": "^1.1.7"

      },

      "engines": {

        "node": ">=14.0.0"

      },

      "peerDependencies": {

        "postcss": "^8.0.0"

      }

    },

    "node_modules/postcss-js": {

      "version": "4.0.1",

      "resolved": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz",

      "integrity": "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "camelcase-css": "^2.0.1"

      },

      "engines": {

        "node": "^12 || ^14 || >= 16"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/postcss/"

      },

      "peerDependencies": {

        "postcss": "^8.4.21"

      }

    },

    "node_modules/postcss-load-config": {

      "version": "4.0.2",

      "resolved": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz",

      "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/postcss/"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "lilconfig": "^3.0.0",

        "yaml": "^2.3.4"

      },

      "engines": {

        "node": ">= 14"

      },

      "peerDependencies": {

        "postcss": ">=8.0.9",

        "ts-node": ">=9.0.0"

      },

      "peerDependenciesMeta": {

        "postcss": {

          "optional": true

        },

        "ts-node": {

          "optional": true

        }

      }

    },

    "node_modules/postcss-nested": {

      "version": "6.2.0",

      "resolved": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz",

      "integrity": "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/postcss/"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "postcss-selector-parser": "^6.1.1"

      },

      "engines": {

        "node": ">=12.0"

      },

      "peerDependencies": {

        "postcss": "^8.2.14"

      }

    },

    "node_modules/postcss-selector-parser": {

      "version": "6.1.2",

      "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz",

      "integrity": "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "cssesc": "^3.0.0",

        "util-deprecate": "^1.0.2"

      },

      "engines": {

        "node": ">=4"

      }

    },

    "node_modules/postcss-value-parser": {

      "version": "4.2.0",

      "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz",

      "integrity": "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/prelude-ls": {

      "version": "1.2.1",

      "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz",

      "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 0.8.0"

      }

    },

    "node_modules/prettier": {

      "version": "3.6.2",

      "resolved": "https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz",

      "integrity": "sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==",

      "dev": true,

      "license": "MIT",

      "bin": {

        "prettier": "bin/prettier.cjs"

      },

      "engines": {

        "node": ">=14"

      },

      "funding": {

        "url": "https://github.com/prettier/prettier?sponsor=1"

      }

    },

    "node_modules/property-information": {

      "version": "7.1.0",

      "resolved": "https://registry.npmjs.org/property-information/-/property-information-7.1.0.tgz",

      "integrity": "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/punycode": {

      "version": "2.3.1",

      "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz",

      "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=6"

      }

    },

    "node_modules/queue-microtask": {

      "version": "1.2.3",

      "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz",

      "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",

      "dev": true,

      "funding": [

        {

          "type": "github",

          "url": "https://github.com/sponsors/feross"

        },

        {

          "type": "patreon",

          "url": "https://www.patreon.com/feross"

        },

        {

          "type": "consulting",

          "url": "https://feross.org/support"

        }

      ],

      "license": "MIT"

    },

    "node_modules/react": {

      "version": "18.3.1",

      "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz",

      "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==",

      "license": "MIT",

      "dependencies": {

        "loose-envify": "^1.1.0"

      },

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/react-dom": {

      "version": "18.3.1",

      "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz",

      "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==",

      "license": "MIT",

      "dependencies": {

        "loose-envify": "^1.1.0",

        "scheduler": "^0.23.2"

      },

      "peerDependencies": {

        "react": "^18.3.1"

      }

    },

    "node_modules/react-markdown": {

      "version": "9.1.0",

      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-9.1.0.tgz",

      "integrity": "sha512-xaijuJB0kzGiUdG7nc2MOMDUDBWPyGAjZtUrow9XxUeua8IqeP+VlIfAZ3bphpcLTnSZXz6z9jcVC/TCwbfgdw==",

      "license": "MIT",

      "dependencies": {

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "devlop": "^1.0.0",

        "hast-util-to-jsx-runtime": "^2.0.0",

        "html-url-attributes": "^3.0.0",

        "mdast-util-to-hast": "^13.0.0",

        "remark-parse": "^11.0.0",

        "remark-rehype": "^11.0.0",

        "unified": "^11.0.0",

        "unist-util-visit": "^5.0.0",

        "vfile": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      },

      "peerDependencies": {

        "@types/react": ">=18",

        "react": ">=18"

      }

    },

    "node_modules/react-refresh": {

      "version": "0.17.0",

      "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz",

      "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/read-cache": {

      "version": "1.0.0",

      "resolved": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz",

      "integrity": "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "pify": "^2.3.0"

      }

    },

    "node_modules/readdirp": {

      "version": "3.6.0",

      "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz",

      "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "picomatch": "^2.2.1"

      },

      "engines": {

        "node": ">=8.10.0"

      }

    },

    "node_modules/remark-parse": {

      "version": "11.0.0",

      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz",

      "integrity": "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==",

      "license": "MIT",

      "dependencies": {

        "@types/mdast": "^4.0.0",

        "mdast-util-from-markdown": "^2.0.0",

        "micromark-util-types": "^2.0.0",

        "unified": "^11.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/remark-rehype": {

      "version": "11.1.2",

      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz",

      "integrity": "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==",

      "license": "MIT",

      "dependencies": {

        "@types/hast": "^3.0.0",

        "@types/mdast": "^4.0.0",

        "mdast-util-to-hast": "^13.0.0",

        "unified": "^11.0.0",

        "vfile": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/resolve": {

      "version": "1.22.10",

      "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz",

      "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "is-core-module": "^2.16.0",

        "path-parse": "^1.0.7",

        "supports-preserve-symlinks-flag": "^1.0.0"

      },

      "bin": {

        "resolve": "bin/resolve"

      },

      "engines": {

        "node": ">= 0.4"

      },

      "funding": {

        "url": "https://github.com/sponsors/ljharb"

      }

    },

    "node_modules/resolve-from": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz",

      "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=4"

      }

    },

    "node_modules/reusify": {

      "version": "1.1.0",

      "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz",

      "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "iojs": ">=1.0.0",

        "node": ">=0.10.0"

      }

    },

    "node_modules/rimraf": {

      "version": "3.0.2",

      "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz",

      "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==",

      "deprecated": "Rimraf versions prior to v4 are no longer supported",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "glob": "^7.1.3"

      },

      "bin": {

        "rimraf": "bin.js"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/rollup": {

      "version": "4.45.1",

      "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.45.1.tgz",

      "integrity": "sha512-4iya7Jb76fVpQyLoiVpzUrsjQ12r3dM7fIVz+4NwoYvZOShknRmiv+iu9CClZml5ZLGb0XMcYLutK6w9tgxHDw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@types/estree": "1.0.8"

      },

      "bin": {

        "rollup": "dist/bin/rollup"

      },

      "engines": {

        "node": ">=18.0.0",

        "npm": ">=8.0.0"

      },

      "optionalDependencies": {

        "@rollup/rollup-android-arm-eabi": "4.45.1",

        "@rollup/rollup-android-arm64": "4.45.1",

        "@rollup/rollup-darwin-arm64": "4.45.1",

        "@rollup/rollup-darwin-x64": "4.45.1",

        "@rollup/rollup-freebsd-arm64": "4.45.1",

        "@rollup/rollup-freebsd-x64": "4.45.1",

        "@rollup/rollup-linux-arm-gnueabihf": "4.45.1",

        "@rollup/rollup-linux-arm-musleabihf": "4.45.1",

        "@rollup/rollup-linux-arm64-gnu": "4.45.1",

        "@rollup/rollup-linux-arm64-musl": "4.45.1",

        "@rollup/rollup-linux-loongarch64-gnu": "4.45.1",

        "@rollup/rollup-linux-powerpc64le-gnu": "4.45.1",

        "@rollup/rollup-linux-riscv64-gnu": "4.45.1",

        "@rollup/rollup-linux-riscv64-musl": "4.45.1",

        "@rollup/rollup-linux-s390x-gnu": "4.45.1",

        "@rollup/rollup-linux-x64-gnu": "4.45.1",

        "@rollup/rollup-linux-x64-musl": "4.45.1",

        "@rollup/rollup-win32-arm64-msvc": "4.45.1",

        "@rollup/rollup-win32-ia32-msvc": "4.45.1",

        "@rollup/rollup-win32-x64-msvc": "4.45.1",

        "fsevents": "~2.3.2"

      }

    },

    "node_modules/run-parallel": {

      "version": "1.2.0",

      "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz",

      "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",

      "dev": true,

      "funding": [

        {

          "type": "github",

          "url": "https://github.com/sponsors/feross"

        },

        {

          "type": "patreon",

          "url": "https://www.patreon.com/feross"

        },

        {

          "type": "consulting",

          "url": "https://feross.org/support"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "queue-microtask": "^1.2.2"

      }

    },

    "node_modules/scheduler": {

      "version": "0.23.2",

      "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz",

      "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==",

      "license": "MIT",

      "dependencies": {

        "loose-envify": "^1.1.0"

      }

    },

    "node_modules/semver": {

      "version": "7.7.2",

      "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz",

      "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==",

      "dev": true,

      "license": "ISC",

      "bin": {

        "semver": "bin/semver.js"

      },

      "engines": {

        "node": ">=10"

      }

    },

    "node_modules/shebang-command": {

      "version": "2.0.0",

      "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",

      "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "shebang-regex": "^3.0.0"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/shebang-regex": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz",

      "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/signal-exit": {

      "version": "4.1.0",

      "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz",

      "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",

      "dev": true,

      "license": "ISC",

      "engines": {

        "node": ">=14"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/slash": {

      "version": "3.0.0",

      "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz",

      "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/source-map-js": {

      "version": "1.2.1",

      "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz",

      "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==",

      "dev": true,

      "license": "BSD-3-Clause",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/space-separated-tokens": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz",

      "integrity": "sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/string-width": {

      "version": "5.1.2",

      "resolved": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz",

      "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "eastasianwidth": "^0.2.0",

        "emoji-regex": "^9.2.2",

        "strip-ansi": "^7.0.1"

      },

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/string-width-cjs": {

      "name": "string-width",

      "version": "4.2.3",

      "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",

      "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "emoji-regex": "^8.0.0",

        "is-fullwidth-code-point": "^3.0.0",

        "strip-ansi": "^6.0.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/string-width-cjs/node_modules/emoji-regex": {

      "version": "8.0.0",

      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",

      "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/string-width/node_modules/ansi-regex": {

      "version": "6.1.0",

      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",

      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/ansi-regex?sponsor=1"

      }

    },

    "node_modules/string-width/node_modules/strip-ansi": {

      "version": "7.1.0",

      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",

      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-regex": "^6.0.1"

      },

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/strip-ansi?sponsor=1"

      }

    },

    "node_modules/stringify-entities": {

      "version": "4.0.4",

      "resolved": "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz",

      "integrity": "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==",

      "license": "MIT",

      "dependencies": {

        "character-entities-html4": "^2.0.0",

        "character-entities-legacy": "^3.0.0"

      },

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/strip-ansi": {

      "version": "6.0.1",

      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",

      "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-regex": "^5.0.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/strip-ansi-cjs": {

      "name": "strip-ansi",

      "version": "6.0.1",

      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",

      "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-regex": "^5.0.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/strip-json-comments": {

      "version": "3.1.1",

      "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz",

      "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=8"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/style-to-js": {

      "version": "1.1.17",

      "resolved": "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.17.tgz",

      "integrity": "sha512-xQcBGDxJb6jjFCTzvQtfiPn6YvvP2O8U1MDIPNfJQlWMYfktPy+iGsHE7cssjs7y84d9fQaK4UF3RIJaAHSoYA==",

      "license": "MIT",

      "dependencies": {

        "style-to-object": "1.0.9"

      }

    },

    "node_modules/style-to-object": {

      "version": "1.0.9",

      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.9.tgz",

      "integrity": "sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==",

      "license": "MIT",

      "dependencies": {

        "inline-style-parser": "0.2.4"

      }

    },

    "node_modules/sucrase": {

      "version": "3.35.0",

      "resolved": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz",

      "integrity": "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@jridgewell/gen-mapping": "^0.3.2",

        "commander": "^4.0.0",

        "glob": "^10.3.10",

        "lines-and-columns": "^1.1.6",

        "mz": "^2.7.0",

        "pirates": "^4.0.1",

        "ts-interface-checker": "^0.1.9"

      },

      "bin": {

        "sucrase": "bin/sucrase",

        "sucrase-node": "bin/sucrase-node"

      },

      "engines": {

        "node": ">=16 || 14 >=14.17"

      }

    },

    "node_modules/sucrase/node_modules/glob": {

      "version": "10.4.5",

      "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz",

      "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "foreground-child": "^3.1.0",

        "jackspeak": "^3.1.2",

        "minimatch": "^9.0.4",

        "minipass": "^7.1.2",

        "package-json-from-dist": "^1.0.0",

        "path-scurry": "^1.11.1"

      },

      "bin": {

        "glob": "dist/esm/bin.mjs"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/sucrase/node_modules/minimatch": {

      "version": "9.0.5",

      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz",

      "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "brace-expansion": "^2.0.1"

      },

      "engines": {

        "node": ">=16 || 14 >=14.17"

      },

      "funding": {

        "url": "https://github.com/sponsors/isaacs"

      }

    },

    "node_modules/supports-color": {

      "version": "7.2.0",

      "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz",

      "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "has-flag": "^4.0.0"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/supports-preserve-symlinks-flag": {

      "version": "1.0.0",

      "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz",

      "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">= 0.4"

      },

      "funding": {

        "url": "https://github.com/sponsors/ljharb"

      }

    },

    "node_modules/tailwind-merge": {

      "version": "2.6.0",

      "resolved": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz",

      "integrity": "sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/dcastil"

      }

    },

    "node_modules/tailwindcss": {

      "version": "3.4.17",

      "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz",

      "integrity": "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "@alloc/quick-lru": "^5.2.0",

        "arg": "^5.0.2",

        "chokidar": "^3.6.0",

        "didyoumean": "^1.2.2",

        "dlv": "^1.1.3",

        "fast-glob": "^3.3.2",

        "glob-parent": "^6.0.2",

        "is-glob": "^4.0.3",

        "jiti": "^1.21.6",

        "lilconfig": "^3.1.3",

        "micromatch": "^4.0.8",

        "normalize-path": "^3.0.0",

        "object-hash": "^3.0.0",

        "picocolors": "^1.1.1",

        "postcss": "^8.4.47",

        "postcss-import": "^15.1.0",

        "postcss-js": "^4.0.1",

        "postcss-load-config": "^4.0.2",

        "postcss-nested": "^6.2.0",

        "postcss-selector-parser": "^6.1.2",

        "resolve": "^1.22.8",

        "sucrase": "^3.35.0"

      },

      "bin": {

        "tailwind": "lib/cli.js",

        "tailwindcss": "lib/cli.js"

      },

      "engines": {

        "node": ">=14.0.0"

      }

    },

    "node_modules/text-table": {

      "version": "0.2.0",

      "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz",

      "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/thenify": {

      "version": "3.3.1",

      "resolved": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz",

      "integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "any-promise": "^1.0.0"

      }

    },

    "node_modules/thenify-all": {

      "version": "1.6.0",

      "resolved": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz",

      "integrity": "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "thenify": ">= 3.1.0 < 4"

      },

      "engines": {

        "node": ">=0.8"

      }

    },

    "node_modules/to-regex-range": {

      "version": "5.0.1",

      "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",

      "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "is-number": "^7.0.0"

      },

      "engines": {

        "node": ">=8.0"

      }

    },

    "node_modules/trim-lines": {

      "version": "3.0.1",

      "resolved": "https://registry.npmjs.org/trim-lines/-/trim-lines-3.0.1.tgz",

      "integrity": "sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/trough": {

      "version": "2.2.0",

      "resolved": "https://registry.npmjs.org/trough/-/trough-2.2.0.tgz",

      "integrity": "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    },

    "node_modules/ts-api-utils": {

      "version": "1.4.3",

      "resolved": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz",

      "integrity": "sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=16"

      },

      "peerDependencies": {

        "typescript": ">=4.2.0"

      }

    },

    "node_modules/ts-interface-checker": {

      "version": "0.1.13",

      "resolved": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz",

      "integrity": "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==",

      "dev": true,

      "license": "Apache-2.0"

    },

    "node_modules/type-check": {

      "version": "0.4.0",

      "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz",

      "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "prelude-ls": "^1.2.1"

      },

      "engines": {

        "node": ">= 0.8.0"

      }

    },

    "node_modules/type-fest": {

      "version": "0.20.2",

      "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz",

      "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==",

      "dev": true,

      "license": "(MIT OR CC0-1.0)",

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/typescript": {

      "version": "5.8.3",

      "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz",

      "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==",

      "dev": true,

      "license": "Apache-2.0",

      "bin": {

        "tsc": "bin/tsc",

        "tsserver": "bin/tsserver"

      },

      "engines": {

        "node": ">=14.17"

      }

    },

    "node_modules/unified": {

      "version": "11.0.5",

      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz",

      "integrity": "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0",

        "bail": "^2.0.0",

        "devlop": "^1.0.0",

        "extend": "^3.0.0",

        "is-plain-obj": "^4.0.0",

        "trough": "^2.0.0",

        "vfile": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/unist-util-is": {

      "version": "6.0.0",

      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",

      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/unist-util-position": {

      "version": "5.0.0",

      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz",

      "integrity": "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/unist-util-stringify-position": {

      "version": "4.0.0",

      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",

      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/unist-util-visit": {

      "version": "5.0.0",

      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",

      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0",

        "unist-util-is": "^6.0.0",

        "unist-util-visit-parents": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/unist-util-visit-parents": {

      "version": "6.0.1",

      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",

      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0",

        "unist-util-is": "^6.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/update-browserslist-db": {

      "version": "1.1.3",

      "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz",

      "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==",

      "dev": true,

      "funding": [

        {

          "type": "opencollective",

          "url": "https://opencollective.com/browserslist"

        },

        {

          "type": "tidelift",

          "url": "https://tidelift.com/funding/github/npm/browserslist"

        },

        {

          "type": "github",

          "url": "https://github.com/sponsors/ai"

        }

      ],

      "license": "MIT",

      "dependencies": {

        "escalade": "^3.2.0",

        "picocolors": "^1.1.1"

      },

      "bin": {

        "update-browserslist-db": "cli.js"

      },

      "peerDependencies": {

        "browserslist": ">= 4.21.0"

      }

    },

    "node_modules/uri-js": {

      "version": "4.4.1",

      "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz",

      "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==",

      "dev": true,

      "license": "BSD-2-Clause",

      "dependencies": {

        "punycode": "^2.1.0"

      }

    },

    "node_modules/use-sync-external-store": {

      "version": "1.5.0",

      "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz",

      "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==",

      "license": "MIT",

      "peerDependencies": {

        "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"

      }

    },

    "node_modules/util-deprecate": {

      "version": "1.0.2",

      "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz",

      "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/vfile": {

      "version": "6.0.3",

      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz",

      "integrity": "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0",

        "vfile-message": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/vfile-message": {

      "version": "4.0.2",

      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",

      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",

      "license": "MIT",

      "dependencies": {

        "@types/unist": "^3.0.0",

        "unist-util-stringify-position": "^4.0.0"

      },

      "funding": {

        "type": "opencollective",

        "url": "https://opencollective.com/unified"

      }

    },

    "node_modules/vite": {

      "version": "5.4.19",

      "resolved": "https://registry.npmjs.org/vite/-/vite-5.4.19.tgz",

      "integrity": "sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "esbuild": "^0.21.3",

        "postcss": "^8.4.43",

        "rollup": "^4.20.0"

      },

      "bin": {

        "vite": "bin/vite.js"

      },

      "engines": {

        "node": "^18.0.0 || >=20.0.0"

      },

      "funding": {

        "url": "https://github.com/vitejs/vite?sponsor=1"

      },

      "optionalDependencies": {

        "fsevents": "~2.3.3"

      },

      "peerDependencies": {

        "@types/node": "^18.0.0 || >=20.0.0",

        "less": "*",

        "lightningcss": "^1.21.0",

        "sass": "*",

        "sass-embedded": "*",

        "stylus": "*",

        "sugarss": "*",

        "terser": "^5.4.0"

      },

      "peerDependenciesMeta": {

        "@types/node": {

          "optional": true

        },

        "less": {

          "optional": true

        },

        "lightningcss": {

          "optional": true

        },

        "sass": {

          "optional": true

        },

        "sass-embedded": {

          "optional": true

        },

        "stylus": {

          "optional": true

        },

        "sugarss": {

          "optional": true

        },

        "terser": {

          "optional": true

        }

      }

    },

    "node_modules/which": {

      "version": "2.0.2",

      "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz",

      "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",

      "dev": true,

      "license": "ISC",

      "dependencies": {

        "isexe": "^2.0.0"

      },

      "bin": {

        "node-which": "bin/node-which"

      },

      "engines": {

        "node": ">= 8"

      }

    },

    "node_modules/word-wrap": {

      "version": "1.2.5",

      "resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz",

      "integrity": "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=0.10.0"

      }

    },

    "node_modules/wrap-ansi": {

      "version": "8.1.0",

      "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz",

      "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-styles": "^6.1.0",

        "string-width": "^5.0.1",

        "strip-ansi": "^7.0.1"

      },

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/wrap-ansi?sponsor=1"

      }

    },

    "node_modules/wrap-ansi-cjs": {

      "name": "wrap-ansi",

      "version": "7.0.0",

      "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz",

      "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-styles": "^4.0.0",

        "string-width": "^4.1.0",

        "strip-ansi": "^6.0.0"

      },

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/chalk/wrap-ansi?sponsor=1"

      }

    },

    "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {

      "version": "8.0.0",

      "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",

      "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",

      "dev": true,

      "license": "MIT"

    },

    "node_modules/wrap-ansi-cjs/node_modules/string-width": {

      "version": "4.2.3",

      "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",

      "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "emoji-regex": "^8.0.0",

        "is-fullwidth-code-point": "^3.0.0",

        "strip-ansi": "^6.0.1"

      },

      "engines": {

        "node": ">=8"

      }

    },

    "node_modules/wrap-ansi/node_modules/ansi-regex": {

      "version": "6.1.0",

      "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",

      "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/ansi-regex?sponsor=1"

      }

    },

    "node_modules/wrap-ansi/node_modules/ansi-styles": {

      "version": "6.2.1",

      "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",

      "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/ansi-styles?sponsor=1"

      }

    },

    "node_modules/wrap-ansi/node_modules/strip-ansi": {

      "version": "7.1.0",

      "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",

      "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",

      "dev": true,

      "license": "MIT",

      "dependencies": {

        "ansi-regex": "^6.0.1"

      },

      "engines": {

        "node": ">=12"

      },

      "funding": {

        "url": "https://github.com/chalk/strip-ansi?sponsor=1"

      }

    },

    "node_modules/wrappy": {

      "version": "1.0.2",

      "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz",

      "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/yallist": {

      "version": "3.1.1",

      "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz",

      "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==",

      "dev": true,

      "license": "ISC"

    },

    "node_modules/yaml": {

      "version": "2.8.0",

      "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz",

      "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==",

      "dev": true,

      "license": "ISC",

      "bin": {

        "yaml": "bin.mjs"

      },

      "engines": {

        "node": ">= 14.6"

      }

    },

    "node_modules/yocto-queue": {

      "version": "0.1.0",

      "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",

      "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==",

      "dev": true,

      "license": "MIT",

      "engines": {

        "node": ">=10"

      },

      "funding": {

        "url": "https://github.com/sponsors/sindresorhus"

      }

    },

    "node_modules/zustand": {

      "version": "4.5.7",

      "resolved": "https://registry.npmjs.org/zustand/-/zustand-4.5.7.tgz",

      "integrity": "sha512-CHOUy7mu3lbD6o6LJLfllpjkzhHXSBlX8B9+qPddUsIfeF5S/UZ5q0kmCsnRqT1UHFQZchNFDDzMbQsuesHWlw==",

      "license": "MIT",

      "dependencies": {

        "use-sync-external-store": "^1.2.2"

      },

      "engines": {

        "node": ">=12.7.0"

      },

      "peerDependencies": {

        "@types/react": ">=16.8",

        "immer": ">=9.0.6",

        "react": ">=16.8"

      },

      "peerDependenciesMeta": {

        "@types/react": {

          "optional": true

        },

        "immer": {

          "optional": true

        },

        "react": {

          "optional": true

        }

      }

    },

    "node_modules/zwitch": {

      "version": "2.0.4",

      "resolved": "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz",

      "integrity": "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==",

      "license": "MIT",

      "funding": {

        "type": "github",

        "url": "https://github.com/sponsors/wooorm"

      }

    }

  }

}

>>>>>>>
